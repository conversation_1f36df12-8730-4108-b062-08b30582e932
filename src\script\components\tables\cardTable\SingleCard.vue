<template>
    <section
        class="single-card"
        :class="{
            'is-expand': isExpand,
            'is-active': isActive
        }"
        :style="defaultCardConfig.diyStyle"
        @mouseenter="defaultCardConfig.allowHover && (isActive = true)"
        @mouseleave="defaultCardConfig.allowHover && (isActive = false)"
    >
        <header class="single-card-header" @click.stop="handleCardClick">
            <div class="header-left">
                <!-- 图标 -->
                <img v-if="!isHide('icon')" class="header-icon" :src="cardIcon" alt="" />
                <!-- 标题 -->
                <span v-if="!isHide('title')" class="header-title">{{ cardData.name }}</span>
                <!-- 标签 -->
                <span
                    v-if="!isHide('tag')"
                    class="header-tag"
                    :class="tagMap[cardData.rawData.toolSource].class"
                    >{{ tagMap[cardData.rawData.toolSource].name }}
                </span>
                <!-- 更新时间 -->
                <span v-if="!isHide('updateTime')" class="header-update-time"
                    >近期更新：{{ cardData.lastUpdateTime }}
                </span>
            </div>
            <div class="header-right">
                <!-- 复制 -->
                <div v-if="!isHide('copy')" class="header-copy" @click.stop="handleCopyClick">
                    <i class="el-icon-document-copy"></i>
                </div>
                <!-- 编辑 -->
                <div v-if="!isHide('edit')" class="header-edit" @click.stop="handleEditClick">
                    <img class="header-edit-icon" src="@/img/common/icon-pen.png" alt="" />
                </div>
                <!-- 取消 -->
                <el-button
                    v-if="!isHide('cancel')"
                    type="plain"
                    class="header-btn"
                    @click.native.stop="handleCancel"
                >
                    取消
                </el-button>
                <!-- 保存 -->
                <el-button
                    v-if="!isHide('save')"
                    type="plain"
                    class="header-btn save-btn"
                    @click.native.stop="handleSave"
                >
                    保存
                </el-button>
                <!-- 删除 -->
                <div v-if="!isHide('delete')" class="header-delete" @click.stop="handleDelete">
                    <i class="el-icon-delete"></i>
                </div>
                <!-- 设置 -->
                <div
                    v-if="!isHide('setting')"
                    class="header-setting"
                    @click.stop="handleSettingClick"
                >
                    <i class="el-icon-setting"></i>
                </div>
                <!-- 租户密钥 -->
                <div
                    v-if="!isHide('tenantKey')"
                    class="header-tenant-key"
                    @click.stop="handleTenantKeyClick"
                >
                    <img src="@/img/common/key.png" alt="" />
                    <span v-if="cardData.rawData.hasExpireSecretKey">租户有密钥过期</span>
                </div>
                <!-- 生效状态 -->
                <div v-if="!isHide('validate')" class="header-validate" @click.stop>
                    <el-switch
                        v-model="isValid"
                        active-color="#1565ff"
                        inactive-color="#d6dae0"
                        @change="handleValidChange"
                    ></el-switch>
                    {{ validStatusText }}
                </div>
                <!-- 展开 -->
                <i
                    v-if="!isHide('expandIcon') && defaultCardConfig.expandType === 'expand'"
                    class="el-icon-arrow-down header-expand-icon"
                    :class="{ rotate180: isExpand }"
                    @click.stop="handleExpandClick"
                ></i>
                <!-- 链接 -->
                <i
                    v-else-if="!isHide('expandIcon') && defaultCardConfig.expandType === 'link'"
                    class="el-icon-arrow-right header-expand-icon"
                    @click.stop="handleLinkClick"
                ></i>
            </div>
        </header>
        <main class="single-card-content">
            <!-- 默认内容 -->
            <template v-if="!defaultCardConfig.diyContent">
                <div v-if="!isExpand" class="content-introduce" @click.stop="handleCardClick">
                    <span class="content-introduce-text text-ellipsis"
                        >{{ cardData.description }}
                    </span>
                    <span class="content-create-time">创建时间：{{ cardData.createdTime }}</span>
                </div>
                <div v-else-if="!defaultCardConfig.noCardNav">
                    <CardNav v-model="nowNav" :list="cardNavList" />
                    <component
                        v-if="dynamicComponents[nowNav]"
                        :ref="`${nowNav}Ref-${cardData.id}`"
                        class="content-main"
                        :is="nowNav"
                        v-model="cardDetail"
                        :isEdit="isEdit"
                        :readonly="defaultCardConfig.readonly"
                        @updateCard="getCardDetail"
                    />
                </div>
                <div v-else>
                    <slot name="expandContent" :cardData="cardData" :isEdit="isEdit"></slot>
                </div>
            </template>
            <!-- 自定义内容 -->
            <template v-else>
                <slot name="diyContent" :cardData="cardData"></slot>
            </template>
        </main>
    </section>
</template>
<script>
import CardNav from './CardNav.vue';

export default {
    name: 'SingleCard',
    components: {
        CardNav
    },
    props: {
        data: {
            type: Object,
            default: () => ({})
        },
        cardConfig: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            defaultCardConfig: {
                module: '',
                icon: {
                    0: require('@/img/common/order-close.png'),
                    1: require('@/img/common/order-open.png')
                },
                diyStyle: {},
                readonly: false,
                allowHover: false, // 是否允许鼠标悬浮
                expandType: 'expand', // ['expand', 'link']
                editType: 'normal', // ['normal', 'page']
                noCardNav: false, // 是否不显示卡片导航
                diyContent: false, // 是否自定义内容
                headerHideItem: ['tenantKey', 'tag'], // 隐藏的头部元素 ['icon', 'title', 'updateTime', 'edit', 'cancel', 'save', 'delete', 'setting', 'tenantKey', 'validate', 'expandIcon']
                navList: [
                    {
                        name: '基础信息',
                        value: 'BaseInfo'
                    }
                ],
                navListFilterFun: null // (data, navList) => {...}
            },
            isExpand: false,
            isActive: false,
            isEdit: false,
            isValid: false,
            tagMap: {
                1: { name: '内部自增', class: 'tag-in' },
                2: { name: '外部接入', class: 'tag-out' }
            },
            nowNav: '',
            dynamicComponents: {}, // 缓存已加载的组件
            cardData: {
                id: '',
                name: '',
                description: '',
                isValid: 0,
                createdTime: '',
                lastUpdateTime: ''
            },
            cardDetail: {}
        };
    },
    computed: {
        cardIcon() {
            return this.defaultCardConfig.icon[+this.isValid];
        },
        cardNavList() {
            if (!this.defaultCardConfig.navListFilterFun) {
                return this.defaultCardConfig.navList;
            }
            const newNavList = this.defaultCardConfig.navListFilterFun(
                this.data,
                this.defaultCardConfig.navList
            );
            return newNavList.filter((item) => item.isShow !== false);
        },
        validStatusText() {
            const map = { true: '启用', false: '停用' };
            return map[this.isValid];
        },
        // 工具管理的特殊处理：外部工具不能进行修改、删除，生效状态改变
        toolIsOutSource() {
            return this.cardData.rawData.toolSource === 2;
        }
    },
    watch: {
        data: {
            handler(newVal) {
                this.initCard();
            },
            immediate: true,
            deep: true
        },
        isExpand: {
            handler(newVal) {
                if (newVal) {
                    this.getCardDetail();
                } else {
                    this.isEdit = false;
                }
            }
        },
        cardNavList: {
            handler(newVal) {
                if (newVal.length > 0) {
                    this.nowNav = newVal[0].value;
                    this.loadDynamicComponents();
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 判断给定名称的头部元素是否隐藏
        /* eslint-disable complexity */
        isHide(name) {
            // 基础隐藏判断：配置项中显式隐藏
            const baseHide = (this.defaultCardConfig.headerHideItem || []).includes(name);
            if (baseHide) return true;

            // 额外逻辑判断：按按钮类型进行细分控制
            switch (name) {
                case 'copy':
                    return !(this.isExpand || this.isActive);
                case 'edit':
                    if (this.toolIsOutSource) {
                        return true;
                    }
                    return !(this.isExpand || this.isActive) || this.isEdit;
                case 'cancel':
                case 'save':
                    return !this.isEdit || this.defaultCardConfig.editType !== 'normal';
                case 'delete':
                    return !(this.isExpand || this.isActive);
                case 'setting':
                    if (this.toolIsOutSource) {
                        return true;
                    }
                    return !(this.isExpand || this.isActive);
                case 'validate':
                    if (this.toolIsOutSource) {
                        return true;
                    }
                    return false;
                default:
                    return false;
            }
        },
        // 动态加载 cardNavList 中配置的所有组件,约定放在 navItem 文件夹下
        loadDynamicComponents() {
            this.cardNavList.forEach((item) => {
                const componentName = item.value;
                if (!this.dynamicComponents[componentName]) {
                    this.dynamicComponents[componentName] = () =>
                        import(
                            /* webpackChunkName: "nav-item-[request]" */
                            `./navItem/${componentName}.vue`
                        );
                    this.$options.components[componentName] = this.dynamicComponents[componentName];
                }
            });
        },
        initCard() {
            this.defaultCardConfig = {
                ...this.defaultCardConfig,
                ...this.cardConfig
            };
            this.cardData = JSON.parse(JSON.stringify(this.data));
            this.isValid = this.cardData.isValid === 1;
        },
        getCardDetail() {
            this.$emit('cardEvent', {
                type: 'expand',
                name: 'getCardDetail',
                params: this.cardData,
                callback: (e) => (this.cardDetail = e)
            });
        },
        handleCardClick() {
            if (this.defaultCardConfig.expandType !== 'expand') {
                this.handleLinkClick();
                return;
            }
            this.handleExpandClick();
        },
        handleLinkClick() {
            this.$emit('cardEvent', {
                type: 'link',
                name: 'linkCard',
                params: this.cardData,
                callback: null
            });
        },
        handleExpandClick() {
            this.$emit('cardActive', { field: 'isExpand', value: !this.isExpand });
        },
        handleCancel() {
            this.isEdit = false;
            this.getCardDetail();
        },
        async handleSave() {
            let valid = true;
            if (!this.defaultCardConfig.diyContent && !this.defaultCardConfig.noCardNav) {
                const ref = this.$refs[`${this.nowNav}Ref-${this.cardData.id}`];
                valid = await ref.validate();
            }
            if (valid) {
                this.$emit('cardEvent', {
                    type: 'save',
                    name: 'saveCard',
                    params: this.cardDetail,
                    callback: (e) => {
                        e && (this.isEdit = false);
                    }
                });
            }
        },
        handleCopyClick() {
            this.$emit('cardEvent', {
                type: 'copy',
                name: 'copyCard',
                params: this.cardData,
                callback: null
            });
        },
        handleEditClick() {
            if (this.defaultCardConfig.editType === 'normal') {
                this.isEdit = true;
                return;
            }
            if (this.defaultCardConfig.editType === 'page') {
                this.$emit('cardEvent', {
                    type: 'edit',
                    name: 'editCard',
                    params: this.cardData,
                    callback: null
                });
                return;
            }
        },
        handleDelete() {
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, '确定删除吗？')
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        '删除后将无法恢复'
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                this.$emit('cardEvent', {
                    type: 'delete',
                    name: 'deleteCard',
                    params: this.cardData.id,
                    callback: null
                });
            });
        },
        handleSettingClick() {
            this.$emit('cardEvent', {
                type: 'setting',
                name: 'settingCard',
                params: this.cardData,
                callback: null
            });
        },
        handleTenantKeyClick() {
            this.$emit('cardEvent', {
                type: 'tenantKey',
                name: 'tenantKeyCard',
                params: this.cardData,
                callback: null
            });
        },
        handleValidChange(newVal) {
            this.isValid = !newVal;
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, '确定更改状态吗？')
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        '确定后状态为：' + ((newVal && '启用') || '停用')
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                this.$emit('cardEvent', {
                    type: 'valid',
                    name: 'validCard',
                    params: {
                        id: this.cardData.id,
                        isValid: newVal
                    },
                    callback: null
                });
            });
        }
    }
};
</script>
<style scoped lang="less">
.single-card {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.96);
    border: 0.0625rem solid transparent;
    border-radius: 0.375rem;
    overflow: hidden;
    flex: none;
    &.is-expand,
    &.is-active {
        box-shadow: 0 0.25rem 0.5rem 0 rgba(0, 54, 159, 0.1) !important;
        border: 0.0625rem solid rgba(21, 101, 255, 0.6) !important;
    }
    &-header {
        height: 3.25rem;
        background: rgba(255, 255, 255, 0.96);
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        .header-left,
        .header-right {
            display: flex;
            align-items: center;
        }
        .header-left {
            gap: 0.5rem;
            .header-icon {
                width: 1.5rem;
                height: 1.5rem;
            }
            .header-title {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 1rem;
                color: rgba(0, 0, 0, 0.85);
            }
            .header-tag {
                width: 3.75rem;
                height: 1.125rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 0.25rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 0.75rem;
                line-height: 1.25rem;
                user-select: none;
                &.tag-in {
                    color: #0dc05c;
                    background-color: #0dc05c1a;
                    border: 0.0625rem solid #0dc05c99;
                }
                &.tag-out {
                    color: #1565ff;
                    background-color: #1565ff1a;
                    border: 0.0625rem solid #1565ff99;
                }
            }
            .header-update-time {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: #ff7802;
                line-height: 1.25rem;
            }
        }
        .header-right {
            user-select: none;
            gap: 1rem;
            .header-copy {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-document-copy {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-document-copy {
                        color: #1565ff;
                    }
                }
            }
            .header-edit {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                &-icon {
                    width: 1rem;
                    height: 1rem;
                }
            }
            .header-btn {
                margin: 0;
                &.save-btn {
                    background: #ffffff;
                    border-radius: 0.25rem;
                    border: 0.0625rem solid #1565ff99;
                    color: #1565ff;
                    &:hover {
                        background: #1565ff1a;
                    }
                }
            }
            .header-delete {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-delete {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-delete {
                        color: #f74041;
                    }
                }
            }
            .header-setting {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-setting {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-setting {
                        color: #1565ff;
                    }
                }
            }
            .header-tenant-key {
                height: 2rem;
                padding: 0 0.5rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                cursor: pointer;
                img {
                    width: 1rem;
                    height: 1rem;
                }
                span {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 0.875rem;
                    color: #f74041;
                }
            }
            .header-validate {
                height: 2rem;
                padding: 0 0.5rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.65);
            }
            .header-expand-icon {
                font-size: 1rem;
                color: rgba(0, 0, 0, 0.65);
                cursor: pointer;
                transition: transform 0.3s ease-in-out;
                &.rotate180 {
                    transform: rotate(-180deg);
                }
            }
        }
    }
    &-content {
        width: 100%;
        height: 0;
        flex: 1;
        .content-main {
            height: 24.75rem;
            padding: 1rem;
            border-top: 0.0625rem solid #ebedf0;
        }
        .content-introduce {
            padding: 0 1rem 1rem 3rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 3.75rem;
            cursor: pointer;
            .content-introduce-text {
                width: 0;
                flex: 1;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.65);
            }
            .content-create-time {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.65);
            }
        }
    }
}
</style>
