---
type: 'always_apply'
---

# Vue.js 项目开发规范文档

## 项目概述

本项目基于 Vue 2.6 + Element UI 2.13 + mtex-rams-core 平台底座开发，采用模块化架构设计。

## 1. 模块创建规范

### 1.1 模块目录结构

新业务模块必须在 `src/script/plugin2x/` 目录下创建，遵循以下结构：

```
src/script/plugin2x/
├── [模块名]/
│   ├── indexPlugin.vue          # 主入口文件（必需）
│   ├── components/              # 模块专用组件（可选）
│   │   ├── ComponentA.vue
│   │   └── ComponentB.vue
│   ├── [子模块名]/              # 子模块（可选）
│   │   └── indexPlugin.vue
│   └── [其他业务文件].vue       # 其他页面文件（可选）
```

### 1.2 文件命名规范

#### 主入口文件

- **必须命名为**: `indexPlugin.vue`
- **作用**: 模块的主入口点，被路由系统自动加载

#### 模块文件夹命名

- **格式**: 小写字母 + 连字符（kebab-case）
- **示例**: `config-tool`、`permission`、`model-test`
- **禁止**: 驼峰命名、下划线、大写字母

#### 组件文件命名

- **格式**: 大驼峰命名（PascalCase）
- **示例**: `NavBar.vue`、`MetricCard.vue`、`AddOrEditTool.vue`
- **规则**:
    - 组件名应该具有描述性
    - 避免使用 `index.vue` 作为组件名
    - 业务组件以功能命名，通用组件以用途命名

### 1.3 Vue 组件规范

#### 组件结构模板

```vue
<template>
    <div class="[模块名]-container">
        <!-- 页面头部 -->
        <header class="[模块名]-container-header">页面标题</header>

        <!-- 搜索/操作区域 -->
        <div class="[模块名]-container-search">
            <!-- 搜索表单和操作按钮 -->
        </div>

        <!-- 主内容区域 -->
        <main class="[模块名]-container-main">
            <!-- 主要内容 -->
        </main>
    </div>
</template>

<script>
export default {
    name: '[ComponentName]', // 大驼峰命名
    components: {
        // 组件注册
    },
    mixins: [
        // 混入注册
    ],
    data() {
        return {
            // 数据定义
        };
    },
    created() {
        // 生命周期钩子
    },
    methods: {
        // 方法定义
    }
};
</script>

<style scoped>
/* 样式定义 */
</style>
```

#### 组件命名约定

- **name 属性**: 使用大驼峰命名，如 `MainIndex`、`ConfigTool`
- **CSS 类名**: 使用模块名作为前缀，如 `.permission-container`、`.config-tool-header`

## 2. 路由配置规范

### 2.1 路由配置文件位置

- **文件路径**: `src/script/enter/routerItems.js`
- **配置方式**: 统一在此文件中添加新路由

### 2.2 路由配置模板

#### 主模块路由（一级路由）

```javascript
{
    path: 'mcpservice/[模块路径]',
    component: loadView('[模块文件夹]/index', false),
    children: [
        // 子路由配置
    ]
}
```

#### 子模块路由（二级路由）

```javascript
{
    path: '[子模块路径]',           // URL 路径段
    name: '[子模块名称]',           // 路由名称，用于编程式导航
    component: loadView('[模块文件夹]/[子模块文件夹]/index', false)
}
```

### 2.3 路由命名规范

#### path 命名

- **格式**: 小写字母 + 连字符
- **示例**: `config-tool`、`permission`、`options-content`
- **规则**: 与模块文件夹名保持一致

#### name 命名

- **格式**: 驼峰命名
- **示例**: `configTool`、`permission`、`optionsContent`
- **规则**: 与 path 对应，但使用驼峰格式

#### component 路径

- **格式**: `loadView('[文件夹路径]/index', false)`
- **示例**: `loadView('config/tool/index', false)`
- **规则**:
    - 路径相对于 `src/script/plugin2x/`
    - 末尾固定为 `/index`
    - 第二个参数固定为 `false`

### 2.4 路由配置示例

```javascript
// src/script/enter/routerItems.js
export default [
    {
        path: 'mcpservice/index',
        component: loadView('main/index', false),
        children: [
            {
                path: 'newModule',
                name: 'newModule',
                component: loadView('new-module/index', false)
            },
            {
                path: 'newModuleDetail',
                name: 'newModuleDetail',
                component: loadView('new-module/detail/index', false)
            }
        ]
    }
];
```

## 3. API 接口规范

### 3.1 API 文件组织

#### 目录结构

```
src/script/api/
├── index.js                    # API 统一导出文件
├── module/                     # 模块化 API 文件
│   ├── [模块名].js            # 各模块的 API 定义
│   └── common.js              # 公共 API
├── mtex-service-config.js     # 服务配置
└── normal-config.js           # 常规配置
```

#### API 模块文件命名

- **格式**: 小写字母 + 连字符 + `.js`
- **示例**: `config-tool.js`、`permission.js`、`home.js`
- **规则**: 与对应的业务模块名保持一致

### 3.2 API 定义规范

```javascript
// src/script/api/module/new-module.js
export default {
    // 获取列表数据
    getNewModuleList: {
        url: '/api/new-module/list',
        method: 'GET'
    },

    // 创建新记录
    createNewModule: {
        url: '/api/new-module/create',
        method: 'POST'
    },

    // 更新记录
    updateNewModule: {
        url: '/api/new-module/update',
        method: 'PUT'
    },

    // 删除记录
    deleteNewModule: {
        url: '/api/new-module/delete',
        method: 'DELETE'
    }
};
```

## 4. 新模块开发完整步骤

### 4.1 开发前准备

1. **需求分析**: 明确模块功能和页面结构
2. **设计评审**: 确认 UI 设计和交互逻辑
3. **技术方案**: 确定使用的组件和技术栈

### 4.2 开发步骤检查清单

#### 步骤 1: 创建模块目录

- [ ] 在 `src/script/plugin2x/` 下创建模块文件夹
- [ ] 文件夹命名符合 kebab-case 规范
- [ ] 创建 `indexPlugin.vue` 主入口文件

#### 步骤 2: 开发主入口组件

- [ ] 使用标准组件结构模板
- [ ] 设置正确的组件 name 属性
- [ ] 添加模块特有的 CSS 类名前缀
- [ ] 实现基本的页面布局

#### 步骤 3: 配置路由

- [ ] 在 `routerItems.js` 中添加路由配置
- [ ] path 和 name 命名符合规范
- [ ] component 路径配置正确
- [ ] 测试路由跳转功能

#### 步骤 4: 创建 API 接口

- [ ] 在 `src/script/api/module/` 下创建 API 文件
- [ ] 文件命名与模块名保持一致
- [ ] 定义完整的 CRUD 接口
- [ ] 测试接口调用

#### 步骤 5: 开发业务组件

- [ ] 创建 `components` 子目录（如需要）
- [ ] 组件命名使用 PascalCase
- [ ] 实现组件功能和样式
- [ ] 添加必要的 props 和 events

#### 步骤 6: 集成测试

- [ ] 功能测试完整
- [ ] 样式在不同分辨率下正常
- [ ] 错误处理机制完善
- [ ] 性能优化检查

### 4.3 代码质量检查

#### ESLint 规则遵循

- [ ] 代码通过 ESLint 检查
- [ ] 函数复杂度不超过 15
- [ ] 函数参数不超过 7 个
- [ ] 嵌套层次不超过 3 层
- [ ] 避免使用三元表达式（根据项目配置）

#### 代码风格

- [ ] 使用单引号
- [ ] 正确的缩进格式
- [ ] 变量命名具有描述性
- [ ] 添加必要的注释

## 5. 常见问题和解决方案

### 5.1 路由问题

**问题**: 新添加的路由无法访问
**解决方案**:

1. 检查 `routerItems.js` 中的路径配置
2. 确认 `indexPlugin.vue` 文件存在
3. 验证 `loadView` 函数的路径参数

**问题**: 路由跳转后页面空白
**解决方案**:

1. 检查组件的 template 是否正确
2. 查看浏览器控制台的错误信息
3. 确认组件的 import 语句正确

### 5.2 组件问题

**问题**: 组件样式冲突
**解决方案**:

1. 使用 scoped 样式
2. 添加模块特有的 CSS 类名前缀
3. 避免使用全局样式选择器

**问题**: 组件数据不响应
**解决方案**:

1. 检查 data 函数返回的对象结构
2. 确认使用 Vue.set 更新嵌套对象
3. 验证计算属性的依赖关系

### 5.3 API 调用问题

**问题**: 接口调用失败
**解决方案**:

1. 检查 API 配置的 URL 和方法
2. 验证请求参数格式
3. 查看网络请求的响应状态

## 6. 最佳实践建议

### 6.1 性能优化

- 使用 v-show 替代频繁切换的 v-if
- 合理使用计算属性缓存复杂计算
- 避免在模板中使用复杂表达式
- 使用 Object.freeze 冻结大型数据对象

### 6.2 可维护性

- 保持组件单一职责
- 提取公共逻辑到 mixins 或 utils
- 使用 props 验证确保数据类型
- 添加有意义的注释和文档

### 6.3 用户体验

- 添加加载状态提示
- 实现错误边界处理
- 提供友好的错误提示信息
- 确保响应式设计兼容性

---

**文档版本**: v1.0  
**最后更新**: 2025-08-27  
**维护人员**: 开发团队
