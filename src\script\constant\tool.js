// 工具来源
const toolSourceOpts = [
    {
        label: '内部工具(MCP平台)',
        value: 1
    },
    {
        label: '外部工具(外部系统)',
        value: 2
    }
];
// 出参处理方式
const paramHandleOpts = [
    {
        label: '删除指定字段',
        value: 0
    },
    {
        label: '提取指定字段',
        value: 1
    }
];

// 调用方式
const serviceMethodOpts = [
    {
        label: 'GET',
        value: 'GET'
    },
    {
        label: 'POST',
        value: 'POST'
    },
    {
        label: 'PUT',
        value: 'PUT'
    },
    {
        label: 'DELETE',
        value: 'DELETE'
    },
    {
        label: 'PATCH',
        value: 'PATCH'
    }
];

// 参数类型
const paramsTypeOpts = {
    raw: [
        { label: 'string', value: 'string' },
        { label: 'int', value: 'int' },
        { label: 'float', value: 'float' },
        { label: 'boolean', value: 'boolean' },
        { label: 'array', value: 'array' },
        { label: 'object', value: 'object' }
    ],
    formdata: [
        // { label: 'file', value: 'file' },
        { label: 'string', value: 'string' },
        { label: 'int', value: 'int' },
        { label: 'float', value: 'float' },
        { label: 'boolean', value: 'boolean' },
        { label: 'array', value: 'array' },
        { label: 'object', value: 'object' }
    ]
};

// 参数传参方式
const paramRequiredOpts = [
    { label: '选填参数', value: 0 },
    { label: '必填参数', value: 1 },
    { label: '默认值回填', value: 2 }
];

export {
    toolSourceOpts,
    paramHandleOpts,
    serviceMethodOpts,
    paramsTypeOpts,
    paramRequiredOpts
};