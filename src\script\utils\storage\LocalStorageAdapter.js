/**
 * localStorage存储适配器
 * 提供基于localStorage的数据存储功能
 */
import { STORAGE_CONFIG } from '../../constant/modelTest/appConfig';

class LocalStorageAdapter {
    constructor() {
        this.prefix = STORAGE_CONFIG.keyPrefix;
        this.currentSessionKey = this.prefix + 'current_session_id';
        this.sessionsKey = this.prefix + 'chat_sessions';
    }

    /**
     * 初始化存储
     */
    async init() {
        // 检查localStorage是否可用
        if (!this.isLocalStorageAvailable()) {
            throw new Error('localStorage不可用');
        }

        // 初始化存储结构
        if (!localStorage.getItem(this.sessionsKey)) {
            localStorage.setItem(this.sessionsKey, JSON.stringify({}));
        }

        if (!localStorage.getItem(this.currentSessionKey)) {
            localStorage.setItem(this.currentSessionKey, '');
        }

        return true;
    }

    /**
     * 检查localStorage是否可用
     */
    isLocalStorageAvailable() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    // ==================== 会话操作 ====================

    /**
     * 保存会话
     */
    async saveSession(session) {
        const sessions = this.getSessions();

        // 如果会话已存在，保留原有的消息（除非明确传入了新的消息）
        if (sessions[session.id]) {
            const existingSession = sessions[session.id];
            // 合并会话数据，保留现有消息（除非新数据中包含消息）
            const updatedSession = {
                ...existingSession,
                ...session
            };

            // 如果新数据中没有消息，保留原有消息
            if (!session.messages && existingSession.messages) {
                updatedSession.messages = existingSession.messages;
            }

            sessions[session.id] = updatedSession;
        } else {
            // 新会话，初始化空消息数组
            session.messages = session.messages || [];
            sessions[session.id] = session;
        }

        localStorage.setItem(this.sessionsKey, JSON.stringify(sessions));
    }

    /**
     * 获取会话
     */
    async getSession(sessionId) {
        const sessions = this.getSessions();
        return sessions[sessionId] || null;
    }

    /**
     * 删除会话
     */
    async deleteSession(sessionId) {
        const sessions = this.getSessions();
        delete sessions[sessionId];
        localStorage.setItem(this.sessionsKey, JSON.stringify(sessions));

        // 如果删除的是当前会话，清空当前会话ID
        const currentSessionId = this.getCurrentSessionId();
        if (currentSessionId === sessionId) {
            this.setCurrentSessionId('');
        }
    }

    /**
     * 获取所有会话
     */
    async getAllSessions() {
        const sessions = this.getSessions();
        return Object.values(sessions);
    }

    /**
     * 获取当前会话ID
     */
    getCurrentSessionId() {
        return localStorage.getItem(this.currentSessionKey) || '';
    }

    /**
     * 设置当前会话ID
     */
    setCurrentSessionId(sessionId) {
        localStorage.setItem(this.currentSessionKey, sessionId);
    }

    /**
     * 获取会话数据
     */
    getSessions() {
        try {
            const data = localStorage.getItem(this.sessionsKey);
            if (data) {
                return JSON.parse(data);
            }
            return {};
        } catch (error) {
            console.error('解析会话数据失败:', error);
            return {};
        }
    }

    // ==================== 消息操作 ====================

    /**
     * 保存消息
     */
    async saveMessage(message) {
        const sessions = this.getSessions();
        const sessionId = message.sessionId;

        // 确保会话存在
        if (!sessions[sessionId]) {
            throw new Error(`会话 ${sessionId} 不存在`);
        }

        // 确保会话有消息数组
        if (!sessions[sessionId].messages) {
            sessions[sessionId].messages = [];
        }

        // 检查消息是否已存在，避免重复添加
        const existingIndex = sessions[sessionId].messages.findIndex(msg => msg.id === message.id);
        if (existingIndex >= 0) {
            // 更新现有消息
            sessions[sessionId].messages[existingIndex] = message;
        } else {
            // 添加新消息
            sessions[sessionId].messages.push(message);
        }

        // 更新会话的更新时间
        sessions[sessionId].updatedAt = Date.now();

        // 保存所有会话数据
        localStorage.setItem(this.sessionsKey, JSON.stringify(sessions));
    }

    /**
     * 获取消息
     */
    async getMessage(messageId) {
        const sessions = this.getSessions();

        // 遍历所有会话的消息来查找
        for (const sessionId in sessions) {
            const session = sessions[sessionId];
            if (session.messages) {
                const message = session.messages.find(msg => msg.id === messageId);
                if (message) {
                    return message;
                }
            }
        }

        return null;
    }

    /**
     * 删除消息
     */
    async deleteMessage(messageId) {
        const sessions = this.getSessions();

        // 遍历所有会话的消息来查找并删除
        for (const sessionId in sessions) {
            const session = sessions[sessionId];
            if (session.messages) {
                const messageIndex = session.messages.findIndex(msg => msg.id === messageId);
                if (messageIndex >= 0) {
                    session.messages.splice(messageIndex, 1);
                    session.updatedAt = Date.now();
                    localStorage.setItem(this.sessionsKey, JSON.stringify(sessions));
                    return;
                }
            }
        }
    }

    /**
     * 根据会话ID获取消息
     */
    async getMessagesBySession(sessionId) {
        const sessions = this.getSessions();
        const session = sessions[sessionId];

        if (session && session.messages) {
            return session.messages;
        }

        return [];
    }

    /**
     * 删除会话的所有消息
     */
    async deleteMessagesBySession(sessionId) {
        const sessions = this.getSessions();

        if (sessions[sessionId]) {
            sessions[sessionId].messages = [];
            sessions[sessionId].updatedAt = Date.now();
            localStorage.setItem(this.sessionsKey, JSON.stringify(sessions));
        }
    }

    /**
     * 获取所有消息（兼容性方法）
     */
    getMessages() {
        const sessions = this.getSessions();
        const allMessages = {};

        for (const sessionId in sessions) {
            const session = sessions[sessionId];
            if (session.messages) {
                session.messages.forEach(message => {
                    allMessages[message.id] = message;
                });
            }
        }

        return allMessages;
    }

    // ==================== 通用操作 ====================

    /**
     * 清空所有数据
     */
    async clear() {
        // 清空会话数据（包含消息）
        localStorage.removeItem(this.sessionsKey);
        localStorage.removeItem(this.currentSessionKey);

        // 重新初始化
        await this.init();
    }

    /**
     * 获取存储使用情况
     */
    getStorageUsage() {
        let totalSize = 0;

        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key) && key.startsWith(this.prefix)) {
                totalSize += localStorage[key].length;
            }
        }

        return {
            used: totalSize,
            // localStorage通常限制为5-10MB，这里假设5MB
            total: 5 * 1024 * 1024,
            percentage: (totalSize / (5 * 1024 * 1024)) * 100
        };
    }

    /**
     * 检查存储空间是否充足
     */
    hasEnoughSpace(estimatedSize = 1024) {
        const usage = this.getStorageUsage();
        return (usage.used + estimatedSize) < usage.total * 0.9; // 保留10%空间
    }
}

export default LocalStorageAdapter;
