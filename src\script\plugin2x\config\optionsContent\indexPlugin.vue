<template>
    <div class="options-content-container">
        <header class="options-content-container-header">选项内容配置</header>
        <div class="options-content-container-search">
            <SearchBar :form-cols="searchFormCols" :form="searchForm">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button
                    style="margin-left: auto"
                    type="primary"
                    icon="el-icon-plus"
                    @click="handleAddField"
                    >新增字段</el-button
                >
            </SearchBar>
        </div>
        <main class="options-content-container-main">
            <InlineCardTable
                v-loading="isLoading"
                :data="optionsTableData"
                :total="total"
                :layout="'total, prev, pager, next, sizes, jumper'"
                :pagination="pagination"
                :updateTable="getTableData"
                :cardConfig="cardConfig"
                @cardEvent="handleCardEvent"
            ></InlineCardTable>
        </main>
        <!-- 新增/编辑字段弹窗 -->
        <el-dialog
            :title="dialogMode === 'add' ? '新增字段' : '编辑字段'"
            :visible.sync="fieldDialogVisible"
            width="736px"
            :close-on-click-modal="false"
            destroy-on-close
            @close="handleDialogClose"
        >
            <SearchBar ref="addFieldFormRef" :form-cols="addFieldFormCols" :form="addFieldForm">
                <template #optionContent>
                    <InputToTag
                        v-model="addFieldForm.optionContent"
                        tagType=""
                        placeholder="输入后按Enter即可创建一个选项"
                    />
                </template>
                <template #optionList>
                    <el-popover
                        placement="right"
                        width="280"
                        trigger="click"
                        v-model="optionPopoverVisible"
                        @show="loadAvailableOptions"
                    >
                        <div class="option-selector">
                            <div class="option-selector-content">
                                <el-tag
                                    v-for="option in availableOptions"
                                    size="small"
                                    :key="option"
                                    :type="isOptionSelected(option) ? 'info' : ''"
                                    class="option-tag"
                                    @click="handleSelectOption(option)"
                                >
                                    {{ option }}
                                </el-tag>
                                <div v-if="availableOptions.length === 0" class="no-options">
                                    暂无可选择的选项
                                </div>
                            </div>
                        </div>
                        <el-button slot="reference" type="plain" style="height: 32px"
                            >选择
                        </el-button>
                    </el-popover>
                </template>
            </SearchBar>
            <div class="dialog-footer">
                <el-button @click="fieldDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSaveField">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import InputToTag from '@/script/components/InputToTag.vue';
import InlineCardTable from '@/script/components/tables/inlineCardTable/index.vue';
import { FITMODULE, OPTIONTYPE } from '@/script/constant/optionsContent/options';
// import OptionsContent from '@/script/api/module/options-content';

export default {
    name: 'OptionsContentConfig',
    components: {
        SearchBar,
        InputToTag,
        InlineCardTable
    },
    data() {
        return {
            isLoading: false,
            // 搜索表单
            searchFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'fieldName',
                        label: '字段名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'fitModule',
                        label: '适用模块：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [{ label: '全部', value: 0 }].concat(FITMODULE)
                    },
                    {
                        type: 'el-select',
                        prop: 'optionType',
                        label: '选项形式：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [{ label: '全部', value: 0 }].concat(OPTIONTYPE)
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 12,
                        isShow: true
                    }
                ]
            ],
            searchForm: {
                fieldName: '',
                fitModule: 0,
                optionType: 0
            },
            // 新增/编辑字段弹窗
            fieldDialogVisible: false,
            dialogMode: 'add', // 'add' | 'edit'
            editingFieldId: null,
            addFieldFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'fieldName',
                        label: '字段名称：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'fitModule',
                        label: '适用模块：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: FITMODULE
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'optionType',
                        label: '选项形式：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: OPTIONTYPE
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'defaultOption',
                        label: '默认选项：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'slot',
                        prop: 'optionContent',
                        label: '选项内容：',
                        labelWidth: '90px',
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'slot',
                        prop: 'optionList',
                        label: '',
                        labelWidth: '',
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            addFieldForm: {
                fieldName: '',
                fitModule: '',
                optionType: '',
                defaultOption: '',
                optionContent: []
            },
            // 选项内容表格数据
            optionsTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            // 卡片配置
            cardConfig: {
                icon: {
                    0: require('../../../../img/common/file-close.png'),
                    1: require('../../../../img/common/file-open.png')
                },
                headerHideItem: []
            },
            // 选项选择器相关
            optionPopoverVisible: false,
            availableOptions: []
        };
    },

    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            // 模拟数据，实际应该调用API
            // 在实际项目中，这些参数会用于API调用
            console.log('分页参数:', { curPage, pageSize });
            const res = {
                serviceFlag: 'TRUE',
                returnCode: '000001',
                returnMsg: '操作成功',
                data: {
                    total: 7,
                    list: [
                        {
                            fieldId: 1,
                            fieldName: '用户类型',
                            fitModule: 1,
                            optionType: 1,
                            defaultOption: '个人用户',
                            optionContent: ['个人用户', '企业用户', '政府用户'],
                            isValid: 1,
                            createdTime: '2025-08-21 17:23:07',
                            lastUpdateTime: '2025-08-21 17:23:07'
                        },
                        {
                            fieldId: 2,
                            fieldName: '服务等级',
                            fitModule: 2,
                            optionType: 2,
                            defaultOption: '标准',
                            optionContent: ['基础', '标准', '高级', '企业'],
                            isValid: 1,
                            createdTime: '2025-08-20 14:39:20',
                            lastUpdateTime: '2025-08-20 14:39:20'
                        },
                        {
                            fieldId: 3,
                            fieldName: '地区选择',
                            fitModule: 1,
                            optionType: 1,
                            defaultOption: '北京',
                            optionContent: ['北京', '上海', '广州', '深圳', '杭州'],
                            isValid: 1,
                            createdTime: '2025-08-19 16:38:29',
                            lastUpdateTime: '2025-08-19 16:38:29'
                        },
                        {
                            fieldId: 4,
                            fieldName: '支付方式',
                            fitModule: 3,
                            optionType: 1,
                            defaultOption: '微信支付',
                            optionContent: ['微信支付', '支付宝', '银行卡', '现金'],
                            isValid: 0,
                            createdTime: '2025-08-15 14:47:55',
                            lastUpdateTime: '2025-08-15 14:47:55'
                        },
                        {
                            fieldId: 5,
                            fieldName: '通知方式',
                            fitModule: 2,
                            optionType: 2,
                            defaultOption: '短信',
                            optionContent: ['短信', '邮件', '微信', '电话'],
                            isValid: 1,
                            createdTime: '2025-08-12 14:07:12',
                            lastUpdateTime: '2025-08-12 14:07:12'
                        },
                        {
                            fieldId: 6,
                            fieldName: '优先级',
                            fitModule: 1,
                            optionType: 1,
                            defaultOption: '中',
                            optionContent: ['低', '中', '高', '紧急'],
                            isValid: 1,
                            createdTime: '2025-07-30 14:15:04',
                            lastUpdateTime: '2025-07-30 14:15:04'
                        },
                        {
                            fieldId: 7,
                            fieldName: '状态',
                            fitModule: 3,
                            optionType: 1,
                            defaultOption: '待处理',
                            optionContent: ['待处理', '处理中', '已完成', '已取消'],
                            isValid: 1,
                            createdTime: '2025-07-28 14:18:06',
                            lastUpdateTime: '2025-07-28 14:18:06'
                        }
                    ]
                }
            };

            this.optionsTableData = res.data.list
                .map((item) => {
                    return {
                        rawData: item,
                        id: item.fieldId,
                        name: item.fieldName,
                        isValid: item.isValid,
                        createdTime: item.createdTime,
                        lastUpdateTime: item.lastUpdateTime,
                        fitModuleLabel: this.getFitModuleLabel(item.fitModule),
                        optionTypeLabel: this.getOptionTypeLabel(item.optionType),
                        defaultOption: item.defaultOption
                    };
                })
                .sort((a, b) => {
                    return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
                });
            this.total = res.data.total;
            // 实际项目中应该调用真实的API
            // this.isLoading = true;
            // OptionsContent.getOptionsContentPage({
            //     pageNum: curPage,
            //     pageSize: pageSize,
            //     fieldName: this.searchForm.fieldName,
            //     fitModule: this.searchForm.fitModule,
            //     optionType: this.searchForm.optionType
            // })
            //     .then((res) => {
            //         if (res.serviceFlag === 'TRUE') {
            //             this.optionsTableData = res.data.list.map((item) => ({
            //                 rawData: item,
            //                 id: item.fieldId,
            //                 name: item.fieldName,
            //                 isValid: item.isValid,
            //                 createdTime: item.createdTime,
            //                 lastUpdateTime: item.lastUpdateTime,
            //                 fitModuleLabel: this.getFitModuleLabel(item.fitModule),
            //                 optionTypeLabel: this.getOptionTypeLabel(item.optionType),
            //                 defaultOption: item.defaultOption
            //             }));
            //             this.total = res.data.total;
            //         }
            //     })
            //     .finally(() => {
            //         this.isLoading = false;
            //     });
        },
        getFitModuleLabel(value) {
            const item = FITMODULE.find((item) => item.value === value);
            if (item) {
                return item.label;
            }
            return value;
        },
        getOptionTypeLabel(value) {
            const item = OPTIONTYPE.find((item) => item.value === value);
            if (item) {
                return item.label;
            }
            return value;
        },
        // 加载可用选项
        loadAvailableOptions() {
            // 实际项目中应该调用API获取所有可用选项
            // OptionsContent.getAllAvailableOptions().then(res => {
            //     if (res.serviceFlag === 'TRUE') {
            //         this.availableOptions = res.data;
            //     }
            // });

            // 模拟数据：从现有数据中汇总所有选项
            const allOptionsSet = new Set();

            // 从当前表格数据中提取所有选项内容
            this.optionsTableData.forEach((item) => {
                if (item.rawData.optionContent && Array.isArray(item.rawData.optionContent)) {
                    item.rawData.optionContent.forEach((option) => {
                        allOptionsSet.add(option);
                    });
                }
            });

            // 转换为数组
            this.availableOptions = Array.from(allOptionsSet);
        },

        // 判断选项是否已被选择
        isOptionSelected(option) {
            return this.addFieldForm.optionContent.includes(option);
        },

        // 处理选项选择
        handleSelectOption(option) {
            if (!this.isOptionSelected(option)) {
                // 添加选项到 InputToTag 组件
                this.addFieldForm.optionContent.push(option);
            }
        },
        // 打开新增字段弹窗
        handleAddField() {
            this.dialogMode = 'add';
            this.editingFieldId = null;
            this.addFieldForm = this.$options.data().addFieldForm;
            this.fieldDialogVisible = true;
        },

        // 打开编辑字段弹窗
        handleEditField(fieldData) {
            this.dialogMode = 'edit';
            this.editingFieldId = fieldData.id;

            // 回显数据
            this.addFieldForm = {
                fieldName: fieldData.name,
                fitModule: fieldData.rawData.fitModule,
                optionType: fieldData.rawData.optionType,
                defaultOption: fieldData.rawData.defaultOption,
                optionContent: [...fieldData.rawData.optionContent] // 深拷贝数组
            };

            this.fieldDialogVisible = true;
        },

        // 保存字段（新增或编辑）
        handleSaveField() {
            this.$refs.addFieldFormRef.validForm().then((valid) => {
                if (valid) {
                    if (this.dialogMode === 'add') {
                        // 新增逻辑
                        // 实际项目中应该调用真实的API
                        // OptionsContent.addOptionsContent(this.addFieldForm).then((res) => {
                        //     if (res.serviceFlag === 'TRUE') {
                        //         this.$message.success(res.returnMsg || '新增成功');
                        //         this.fieldDialogVisible = false;
                        //         this.handleSearch();
                        //     } else {
                        //         this.$message.error(res.returnMsg || '新增失败');
                        //     }
                        // });

                        this.$message.success('新增字段成功');
                    } else {
                        // 编辑逻辑
                        // 实际项目中应该调用真实的API
                        // OptionsContent.updateOptionsContent({
                        //     fieldId: this.editingFieldId,
                        //     ...this.addFieldForm
                        // }).then((res) => {
                        //     if (res.serviceFlag === 'TRUE') {
                        //         this.$message.success(res.returnMsg || '编辑成功');
                        //         this.fieldDialogVisible = false;
                        //         this.handleSearch();
                        //     } else {
                        //         this.$message.error(res.returnMsg || '编辑失败');
                        //     }
                        // });

                        this.$message.success('编辑字段成功');
                    }

                    this.fieldDialogVisible = false;
                    this.handleSearch();
                }
            });
        },

        // 弹窗关闭处理
        handleDialogClose() {
            this.addFieldForm = this.$options.data().addFieldForm;
            this.dialogMode = 'add';
            this.editingFieldId = null;
        },
        handleCardEvent(event) {
            const eventMap = {
                // 编辑卡片
                edit: () => {
                    // 查找对应的字段数据
                    const fieldData = this.optionsTableData.find(
                        (item) => item.id === event.params.id
                    );
                    if (fieldData) {
                        this.handleEditField(fieldData);
                    } else {
                        this.$message.error('未找到字段数据');
                    }
                },

                // 删除卡片
                delete: () => {
                    // 实际项目中应该调用真实的API
                    // OptionsContent.deleteOptionsContent({
                    //     fieldIds: [event.params]
                    // }).then((res) => {
                    //     this.handleResponse(res, '删除成功', '删除失败');
                    // });

                    // 模拟成功响应
                    this.$message.success('删除成功');
                    this.handleSearch();
                },

                // 切换卡片状态（有效/无效）
                valid: () => {
                    // 实际项目中应该调用真实的API
                    // OptionsContent.changeOptionsContentValid({
                    //     fieldIds: [event.params.id],
                    //     isValid: +event.params.isValid
                    // }).then((res) => {
                    //     this.handleResponse(res, '切换成功', '切换失败');
                    // });

                    // 模拟成功响应
                    this.$message.success('状态切换成功');
                    this.handleSearch();
                }
            };

            if (eventMap[event.type]) {
                eventMap[event.type]();
            }
        },
        handleResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.options-content-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}

// 选项选择器样式
.option-selector {
    max-height: 300px;

    &-content {
        max-height: 240px;
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        user-select: none;
        &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 8px;
            background: #8b8b8b;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            border-radius: 8px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }
}

.option-tag {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

.no-options {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    padding: 20px 0;
    width: 100%;
}
</style>
