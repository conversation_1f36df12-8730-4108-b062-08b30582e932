<template>
    <div class="card-nav">
        <div
            class="card-nav-item"
            :class="{ active: value === item.value }"
            v-for="item in list"
            :key="item.value"
            @click="handleClick(item.value)"
        >
            <span class="card-nav-item-text">
                {{ item.name }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CardNav',
    props: {
        list: {
            type: Array,
            default: () => []
        },
        value: {
            type: String,
            default: ''
        }
    },
    data() {
        return {};
    },
    methods: {
        handleClick(value) {
            this.$emit('input', value);
        }
    }
};
</script>

<style lang="less" scoped>
.card-nav {
    height: 3rem;
    background: #f6f8fa;
    display: flex;
    align-items: end;
    gap: 0.25rem;
    padding: 0 1rem;
    .card-nav-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 2.25rem;
        background: transparent;
        border: 0.0625rem solid #d6dae0;
        border-bottom: none;
        border-radius: 0.25rem 0.25rem 0px 0px;
        cursor: pointer;
        user-select: none;

        &.active {
            background: #ffffff;
            box-shadow: inset 0px 0.125rem 0px 0px #1565ff;
            border-radius: 0.25rem 0.25rem 0px 0px;
            border: 0.0625rem solid #ffffff;
            color: #1565ff;
            .card-nav-item-text {
                font-weight: 600;
                color: #1565ff;
            }
            .card-nav-item-text {
                font-weight: 600;
                color: #1565ff;
            }
        }

        .card-nav-item-text {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.65);
            padding: 0.5rem 1.5rem;
        }
    }
}
</style>
