<template>
    <section
        class="single-card"
        :class="{
            'is-active': isActive
        }"
        @mouseenter="isActive = true"
        @mouseleave="isActive = false"
    >
        <header class="single-card-header">
            <div class="header-left">
                <!-- 图标 -->
                <img v-if="!isHide('icon')" class="header-icon" :src="cardIcon" alt="" />
                <!-- 标题 -->
                <span v-if="!isHide('title')" class="header-title text-ellipsis"
                    >{{ cardData.name }}
                </span>
            </div>
            <div class="header-right">
                <!-- 编辑 -->
                <div v-show="!isHide('edit')" class="header-edit" @click.stop="handleEditClick">
                    <img class="header-edit-icon" src="@/img/common/icon-pen.png" alt="" />
                </div>
                <!-- 删除 -->
                <div v-show="!isHide('delete')" class="header-delete" @click.stop="handleDelete">
                    <i class="el-icon-delete"></i>
                </div>
                <!-- 生效状态 -->
                <div v-show="!isHide('validate')" class="header-validate" @click.stop>
                    <el-switch
                        v-model="isValid"
                        active-color="#1565ff"
                        inactive-color="#d6dae0"
                        @change="handleValidChange"
                    ></el-switch>
                </div>
            </div>
        </header>
        <main class="single-card-content">
            <div class="content-form-item">
                适用模块：<span class="content-form-item-value">{{
                    cardData.fitModuleLabel || '-'
                }}</span>
            </div>
            <div class="content-form-item">
                选项形式：<span class="content-form-item-value">{{
                    cardData.optionTypeLabel || '-'
                }}</span>
            </div>
            <div class="content-form-item">
                默认选项：<span class="content-form-item-value">{{
                    cardData.defaultOption || '-'
                }}</span>
            </div>
        </main>
    </section>
</template>
<script>
export default {
    name: 'SingleCard',
    props: {
        data: {
            type: Object,
            default: () => ({})
        },
        cardConfig: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            defaultCardConfig: {
                icon: {
                    0: require('@/img/common/file-close.png'),
                    1: require('@/img/common/file-open.png')
                },
                headerHideItem: []
            },
            isActive: false,
            isValid: false,
            cardData: {
                name: '',
                fitModuleLabel: '',
                optionTypeLabel: '',
                defaultOption: ''
            }
        };
    },
    computed: {
        cardIcon() {
            return this.defaultCardConfig.icon[+this.isValid];
        }
    },
    watch: {
        data: {
            handler() {
                this.initCard();
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 判断给定名称的头部元素是否隐藏
        isHide(name) {
            // 基础隐藏判断：配置项中显式隐藏
            const baseHide = (this.defaultCardConfig.headerHideItem || []).includes(name);
            if (baseHide) return true;

            // 额外逻辑判断：按按钮类型进行细分控制
            switch (name) {
                case 'edit':
                    return !this.isActive;
                case 'delete':
                    return !this.isActive;
                case 'validate':
                    return false;
                default:
                    return false;
            }
        },
        initCard() {
            this.defaultCardConfig = {
                ...this.defaultCardConfig,
                ...this.cardConfig
            };
            this.cardData = JSON.parse(JSON.stringify(this.data));
            this.isValid = this.cardData.isValid === 1;
        },
        handleEditClick() {
            this.$emit('cardEvent', {
                type: 'edit',
                name: 'editCard',
                params: this.cardData,
                callback: null
            });
        },
        handleDelete() {
            this.cardConfirm(
                {
                    title: '确定删除吗？',
                    desc: '删除后将无法恢复'
                },
                () => {
                    this.$emit('cardEvent', {
                        type: 'delete',
                        name: 'deleteCard',
                        params: this.cardData.id,
                        callback: null
                    });
                }
            );
        },
        handleValidChange(newVal) {
            this.isValid = !newVal;
            this.cardConfirm(
                {
                    title: '确定更改状态吗？',
                    desc: '确定后状态为：' + ((newVal && '启用') || '停用')
                },
                () => {
                    this.$emit('cardEvent', {
                        type: 'valid',
                        name: 'validCard',
                        params: {
                            id: this.cardData.id,
                            isValid: newVal
                        },
                        callback: null
                    });
                }
            );
        },
        cardConfirm(messageInfo, callback) {
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, messageInfo.title)
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        messageInfo.desc
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                callback && callback();
            });
        }
    }
};
</script>
<style scoped lang="less">
.single-card {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.96);
    border: 0.0625rem solid transparent;
    border-radius: 0.375rem;
    overflow: hidden;
    flex: none;
    &.is-active {
        box-shadow: 0 0.25rem 0.5rem 0 rgba(0, 54, 159, 0.1) !important;
        border: 0.0625rem solid rgba(21, 101, 255, 0.6) !important;
    }
    &-header {
        height: 3.25rem;
        background: rgba(255, 255, 255, 0.96);
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-left,
        .header-right {
            display: flex;
            align-items: center;
        }
        .header-left {
            gap: 0.5rem;
            .header-icon {
                width: 1.5rem;
                height: 1.5rem;
            }
            .header-title {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 1rem;
                color: rgba(0, 0, 0, 0.85);
            }
        }
        .header-right {
            user-select: none;
            gap: 1rem;
            .header-edit {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                &-icon {
                    width: 1rem;
                    height: 1rem;
                }
            }
            .header-delete {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-delete {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-delete {
                        color: #f74041;
                    }
                }
            }
            .header-validate {
                height: 2rem;
                padding: 0 0.5rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.65);
            }
        }
    }
    &-content {
        width: 100%;
        height: 0;
        flex: 1;
        .content-form-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            font-weight: bold;
            font-size: 0.875rem;
        }
        .content-form-item-value {
            font-weight: normal;
            color: rgba(0, 0, 0, 0.65);
        }
    }
}
</style>
