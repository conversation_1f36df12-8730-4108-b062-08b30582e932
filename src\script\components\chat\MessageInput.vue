<template>
    <div class="message-input">
        <div class="input-container">
            <textarea
                class="chat-input custom-scrollbar"
                v-model="inputText"
                :placeholder="
                    shouldShowStopButton ? '正在生成回复，可以输入下一个问题...' : placeholder
                "
                :disabled="isInputDisabled"
                resize="none"
                rows="2"
                @keydown="handleKeydown"
            ></textarea>
            <div class="input-actions">
                <el-select
                    v-model="currentModel"
                    size="small"
                    class="model-select"
                    :popper-class="popperClass"
                    @change="handleModelChange"
                >
                    <el-option
                        v-for="model in modelOptions"
                        :key="model.value"
                        :label="model.label"
                        :value="model.value"
                    >
                        <div class="model-option" v-if="showModelDetails">
                            <div class="model-option-text">
                                <div class="model-name">{{ model.label }}</div>
                                <div class="model-desc">{{ model.desc }}</div>
                            </div>
                            <i
                                class="el-icon-check model-selected"
                                v-if="currentModel === model.value"
                            ></i>
                        </div>
                        <span v-else>{{ model.label }}</span>
                    </el-option>
                </el-select>
                <el-popover
                    placement="top"
                    width="240"
                    trigger="click"
                    popper-class="mcp-popover mcpservice-theme"
                    v-model="mcpPopoverVisible"
                >
                    <div class="mcp-popover-content">
                        <!-- 搜索框 -->
                        <div class="mcp-search-box">
                            <el-input
                                v-model="mcpSearchText"
                                placeholder="按服务名称检索"
                                size="small"
                                prefix-icon="el-icon-search"
                                clearable
                                @input="handleMcpSearch"
                                @clear="handleMcpSearch"
                            >
                            </el-input>
                        </div>

                        <!-- MCP服务树形列表 -->
                        <div
                            class="mcp-service-tree custom-scrollbar"
                            ref="mcpTreeContainer"
                            @scroll="handleTreeScroll"
                        >
                            <!-- 加载状态 -->
                            <div
                                v-if="mcpLoading && mcpServices.length === 0"
                                class="loading-container"
                            >
                                <i class="el-icon-loading"></i>
                                <span>加载中...</span>
                            </div>

                            <!-- 错误状态 -->
                            <div
                                v-else-if="mcpError && mcpServices.length === 0"
                                class="error-container"
                            >
                                <i class="el-icon-warning"></i>
                                <span>{{ mcpError }}</span>
                                <el-button size="mini" @click="retryLoadMcpServices"
                                    >重试</el-button
                                >
                            </div>

                            <!-- 树形组件 -->
                            <el-tree
                                v-else
                                ref="mcpTree"
                                :data="mcpServices"
                                :props="treeProps"
                                show-checkbox
                                node-key="id"
                                :default-expand-all="false"
                                :check-strictly="false"
                                :lazy="true"
                                :load="lazyLoadNode"
                                :filter-node-method="filterNode"
                                @check-change="handleTreeCheckChange"
                            >
                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                    <span class="node-label text-ellipsis" :title="node.label">{{
                                        node.label
                                    }}</span>
                                    <span
                                        v-if="data.description"
                                        class="node-description text-ellipsis"
                                        :title="data.description"
                                    >
                                        {{ data.description }}
                                    </span>
                                </span>
                            </el-tree>

                            <!-- 没有更多数据提示 -->
                            <div v-if="!hasMoreData && mcpServices.length > 0" class="no-more-data">
                                没有更多数据了
                            </div>
                            <!-- 加载更多指示器 -->
                            <div v-else class="loading-more">
                                <i class="el-icon-loading"></i>
                                <span>加载更多...</span>
                            </div>
                        </div>
                    </div>
                    <div slot="reference" class="mcp-select">
                        <span>选择MCP服务</span>
                        <span v-if="selectedServicesCount > 0" class="selected-count">
                            ({{ selectedServicesCount }})
                        </span>
                    </div>
                </el-popover>
                <div class="right-actions">
                    <!-- 语音 -->
                    <!-- <el-tooltip
                        v-if="showMicButton"
                        class="item"
                        effect="dark"
                        content="语音输入"
                        placement="top"
                        popper-class="mic-popper"
                    >
                        <div class="mic-btn" @click="handleMicClick">
                            <i class="el-icon-microphone"></i>
                        </div>
                    </el-tooltip> -->
                    <!-- 发送/停止按钮 -->
                    <div
                        class="send-btn"
                        :class="{
                            'is-disabled':
                                !shouldShowStopButton && (!inputText.trim() || isSendDisabled),
                            'is-stop': shouldShowStopButton
                        }"
                        @click="handleSendOrStop"
                    >
                        <i :class="shouldShowStopButton ? 'el-icon-close' : 'el-icon-top'"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import ConfigService from '@/script/api/module/config-service';

export default {
    name: 'MessageInput',
    props: {
        // 输入框配置
        placeholder: {
            type: String,
            default: '请输入您的问题，Shift+Enter可换行，Enter发送'
        },
        // 模型选项
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        selectedModel: {
            type: String,
            default: ''
        },

        // 功能开关
        showMicButton: {
            type: Boolean,
            default: false
        },
        showModelDetails: {
            type: Boolean,
            default: false
        },

        // 其他配置
        popperClass: {
            type: String,
            default: 'model-select-popper'
        },
        sendButtonTooltip: {
            type: String,
            default: '请输入你的问题'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        // AI响应状态
        isAiResponding: {
            type: Boolean,
            default: false
        },
        // 流式响应状态
        isStreamResponse: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            inputText: '',
            currentModel: this.selectedModel || this.defaultModel,

            // MCP服务相关数据
            mcpPopoverVisible: false,
            mcpSearchText: '',
            treeProps: {
                children: 'children',
                label: 'label',
                disabled: 'disabled',
                isLeaf: 'leaf'
            },
            selectedServicesCount: 0,
            mcpServices: [],

            // API相关状态
            mcpLoading: false,
            mcpError: null,
            currentPageSize: 10,
            hasMoreData: true,
            searchDebounceTimer: null,

            // 缓存和状态管理
            expandedNodeCache: new Map(),
            checkedKeysCache: [],
            isUpdatingTreeState: false,
            maxCacheSize: 50 // 最大缓存数量
        };
    },

    mounted() {
        // 初始化加载MCP服务列表
        this.loadMcpServices();
    },
    beforeDestroy() {
        this.cleanup();
    },
    computed: {
        // 是否应该显示停止按钮
        shouldShowStopButton() {
            return this.isAiResponding || this.isStreamResponse;
        },
        // 是否应该禁用输入框（只有在disabled prop为true时才禁用）
        isInputDisabled() {
            return this.disabled;
        },
        // 是否应该禁用发送（在AI响应时禁用发送但允许输入）
        isSendDisabled() {
            return this.disabled || this.shouldShowStopButton;
        }
    },
    methods: {
        // ==================== 生命周期和清理方法 ====================

        /**
         * 组件销毁前的清理工作
         * 清理定时器和缓存，防止内存泄漏
         */
        cleanup() {
            if (this.searchDebounceTimer) {
                clearTimeout(this.searchDebounceTimer);
            }
            this.clearAllCaches();
        },

        /**
         * 清理所有缓存数据
         * 包括展开节点缓存和选中状态缓存
         */
        clearAllCaches() {
            this.expandedNodeCache.clear();
            this.checkedKeysCache = [];
        },

        /**
         * 管理缓存大小，防止内存占用过大
         * 当缓存超过最大限制时，删除最旧的缓存项
         */
        manageCacheSize() {
            if (this.expandedNodeCache.size > this.maxCacheSize) {
                // Map保持插入顺序，删除第一个（最旧的）缓存项
                const firstKey = this.expandedNodeCache.keys().next().value;
                this.expandedNodeCache.delete(firstKey);
            }
        },

        /**
         * 添加工具数据到展开节点缓存
         * @param {string} mcpId - MCP服务ID
         * @param {Array} tools - 工具列表
         */
        addToExpandedCache(mcpId, tools) {
            this.manageCacheSize();
            this.expandedNodeCache.set(mcpId, tools);
        },

        // ==================== MCP API相关方法 ====================

        /**
         * 加载MCP服务列表
         * @param {boolean} reset - 是否重置数据（清空现有数据重新加载）
         */
        async loadMcpServices(reset = false) {
            if (this.mcpLoading) return;

            try {
                this.mcpLoading = true;
                this.mcpError = null;

                const currentCheckedKeys = this.getCurrentCheckedKeys(reset);

                if (reset) {
                    this.resetMcpData();
                }

                const response = await this.fetchMcpServices();

                if (response.serviceFlag === 'TRUE') {
                    this.processMcpServicesResponse(response.data, reset, currentCheckedKeys);
                } else {
                    this.handleMcpError(response.returnMsg || '获取MCP服务列表失败');
                }
            } catch (error) {
                console.error('加载MCP服务失败:', error);
                this.handleMcpError('网络错误，请稍后重试');
            } finally {
                this.mcpLoading = false;
            }
        },

        /**
         * 获取当前选中的键值
         * @param {boolean} reset - 是否为重置操作
         * @returns {Array} 选中的节点键值数组
         */
        getCurrentCheckedKeys(reset) {
            if (reset || !this.$refs.mcpTree) return [];
            return this.$refs.mcpTree.getCheckedKeys();
        },

        /**
         * 重置MCP数据和相关状态
         * 清空服务列表、重置分页参数、清理缓存
         */
        resetMcpData() {
            this.mcpServices = [];
            this.currentPageSize = 10;
            this.hasMoreData = true;
            this.clearAllCaches();
        },

        /**
         * 从API获取MCP服务数据
         * @returns {Promise} API响应Promise
         */
        async fetchMcpServices() {
            return await ConfigService.getMCPInfoPage({
                pageNum: 1,
                pageSize: this.currentPageSize,
                mcpDescription: '',
                mcpName: '',
                isValid: 1
            });
        },

        // 处理MCP服务响应
        processMcpServicesResponse(data, reset, currentCheckedKeys) {
            try {
                this.validateMcpData(data);
                const newServices = this.transformMcpData(data.list || []);

                if (reset) {
                    this.mcpServices = newServices;
                } else {
                    this.mergeNewServices(newServices);
                }

                this.hasMoreData = data.list.length === this.currentPageSize;
                this.restoreTreeState(reset, currentCheckedKeys);
            } catch (error) {
                console.error('处理MCP服务响应失败:', error);
                this.handleMcpError('数据处理失败，请重试');
            }
        },

        // 合并新服务数据
        mergeNewServices(newServices) {
            const existingIds = new Set(this.mcpServices.map((s) => s.id));
            const uniqueNewServices = newServices.filter((s) => !existingIds.has(s.id));
            this.mcpServices.push(...uniqueNewServices);
        },

        // 恢复树状态
        restoreTreeState(reset, currentCheckedKeys) {
            this.$nextTick(() => {
                if (reset) {
                    this.restoreCheckedState();
                } else if (currentCheckedKeys.length > 0) {
                    this.restoreCheckedKeys(currentCheckedKeys);
                }
            });
        },

        // 处理MCP错误
        handleMcpError(message) {
            this.mcpError = message;
            this.$message.error(message);
        },

        // 重试加载MCP服务
        retryLoadMcpServices() {
            this.mcpError = null;
            this.loadMcpServices(true);
        },

        // 验证数据完整性
        validateMcpData(data) {
            if (!data || typeof data !== 'object') {
                throw new Error('无效的数据格式');
            }

            if (!Array.isArray(data.list)) {
                throw new Error('服务列表格式错误');
            }

            return true;
        },

        // 验证工具数据
        validateToolData(toolData) {
            if (!toolData || !toolData.toolId || !toolData.toolName) {
                return false;
            }
            return true;
        },

        // 转换API数据为树形结构
        transformMcpData(apiData) {
            return apiData.map((item) => ({
                id: item.mcpId,
                label: item.mcpName,
                disabled: !item.isValid,
                leaf: false,
                isTool: false,
                rawData: item
            }));
        },

        // 加载更多数据
        loadMoreMcpServices: _.debounce(async function () {
            if (!this.hasMoreData || this.mcpLoading) return;

            this.currentPageSize += 10;
            await this.loadMcpServices(false);
        }, 300),

        // 懒加载节点
        async lazyLoadNode(node, resolve) {
            if (node.level === 2) {
                resolve([]);
                return;
            }

            if (node.level === 0) {
                resolve(this.mcpServices);
                return;
            }

            if (node.data.leaf) {
                resolve([]);
                return;
            }

            const mcpId = node.data.id;

            // 检查缓存
            if (this.expandedNodeCache.has(mcpId)) {
                resolve(this.expandedNodeCache.get(mcpId));
                return;
            }

            try {
                node.loading = true;
                const tools = await this.loadNodeTools(mcpId);

                this.addToExpandedCache(mcpId, tools);

                if (tools.length === 0) {
                    node.data.leaf = true;
                }

                resolve(tools);
            } catch (error) {
                console.error('获取MCP服务详情失败:', error);
                resolve([]);
            } finally {
                node.loading = false;
            }
        },

        // 加载节点工具
        async loadNodeTools(mcpId) {
            const response = await ConfigService.getMCPDetails({ mcpId });

            if (response.serviceFlag === 'TRUE') {
                return this.transformToolsData(response.data);
            }

            throw new Error('获取工具列表失败');
        },

        // 转换工具数据
        transformToolsData(detailData) {
            if (!detailData || !detailData.toolList || !Array.isArray(detailData.toolList)) {
                return [];
            }

            return detailData.toolList
                .filter((tool) => tool.isValid && this.validateToolData(tool))
                .map((tool) => ({
                    id: tool.toolId,
                    label: tool.toolName,
                    description: tool.toolDescription || '',
                    disabled: !tool.isValid,
                    isValid: tool.isValid,
                    leaf: true,
                    isTool: true,
                    toolData: tool
                }));
        },

        /**
         * 搜索防抖处理
         * 使用lodash的debounce函数，300ms延迟执行搜索
         */
        handleMcpSearch: _.debounce(function () {
            this.performSearch();
        }, 300),

        /**
         * 执行搜索操作
         * 对树形组件进行过滤，如果搜索为空且无数据则加载数据
         */
        performSearch() {
            const searchText = this.mcpSearchText.trim();

            if (this.$refs.mcpTree) {
                this.$refs.mcpTree.filter(searchText);
            }

            // 如果搜索框为空且当前没有数据，则加载数据
            if (!searchText && this.mcpServices.length === 0) {
                this.loadMcpServices(true);
            }
        },

        /**
         * 树形组件的节点过滤方法
         * @param {string} value - 搜索关键词
         * @param {Object} data - 节点数据
         * @param {Object} node - 节点对象
         * @returns {boolean} 是否显示该节点
         */
        filterNode(value, data, node) {
            if (!value) return true;

            const searchText = value.toLowerCase();
            const currentNodeMatches = this.nodeMatchesSearch(data, searchText);

            if (data.isTool) {
                return this.filterToolNode(currentNodeMatches, node, searchText);
            }

            return this.filterServiceNode(currentNodeMatches, node, searchText);
        },

        // 检查节点是否匹配搜索条件
        nodeMatchesSearch(data, searchText) {
            const label = (data.label || '').toLowerCase();
            const description = (data.description || '').toLowerCase();
            return label.includes(searchText) || description.includes(searchText);
        },

        // 过滤工具节点
        filterToolNode(currentNodeMatches, node, searchText) {
            if (currentNodeMatches) return true;

            const parentNode = node.parent;
            if (parentNode && parentNode.data) {
                return this.nodeMatchesSearch(parentNode.data, searchText);
            }
            return false;
        },

        // 过滤服务节点
        filterServiceNode(currentNodeMatches, node, searchText) {
            if (currentNodeMatches) return true;

            if (node.childNodes && node.childNodes.length > 0) {
                return node.childNodes.some((childNode) => {
                    return (
                        childNode.data &&
                        childNode.data.isTool &&
                        this.nodeMatchesSearch(childNode.data, searchText)
                    );
                });
            }

            return false;
        },

        // ==================== 原有方法 ====================

        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                // Enter键发送消息或停止
                event.preventDefault();
                this.handleSendOrStop();
            }
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim() && !this.isSendDisabled) {
                // 获取选中的MCP服务和工具
                const { mcpServiceId, mcpToolId } = this.getSelectedData();

                const message = {
                    text: this.inputText.trim(),
                    model: this.currentModel,
                    timestamp: new Date().toISOString(),
                    // 添加MCP服务参数到extraParams中
                    extraParams: {
                        mcpServiceId,
                        mcpToolId
                    }
                };

                this.$emit('send-message', message);

                // 清空输入框
                this.inputText = '';
            }
        },

        // 获取选中的数据
        getSelectedData() {
            if (!this.$refs.mcpTree) {
                return {
                    mcpServiceId: [],
                    mcpToolId: []
                };
            }

            try {
                const checkedNodes = this.$refs.mcpTree.getCheckedNodes(false, true);

                const result = {
                    mcpServiceId: [],
                    mcpToolId: []
                };

                checkedNodes.forEach((node) => {
                    if (!node || !node.id) return;

                    if (node.isTool) {
                        result.mcpToolId.push(node.id);
                    } else {
                        result.mcpServiceId.push(node.id);
                    }
                });

                return result;
            } catch (error) {
                console.error('获取选中数据失败:', error);
                return {
                    mcpServiceId: [],
                    mcpToolId: []
                };
            }
        },

        // 处理发送或停止按钮点击
        handleSendOrStop() {
            if (this.shouldShowStopButton) {
                // 如果是停止状态，发送停止事件
                this.$emit('stop-generation');
            } else {
                // 否则发送消息
                this.sendMessage();
            }
        },

        // 处理语音输入点击
        handleMicClick() {
            this.$emit('mic-click');
        },

        // 设置输入文本（外部调用）
        setInputText(text) {
            this.inputText = text;
        },

        // 获取输入文本
        getInputText() {
            return this.inputText;
        },

        // 清空输入框
        clearInput() {
            this.inputText = '';
        },

        // 聚焦输入框
        focus() {
            this.$nextTick(() => {
                const input = this.$el.querySelector('.chat-input');
                if (input) {
                    input.focus();
                }
            });
        },

        // 处理模型变更
        handleModelChange(newModel) {
            this.currentModel = newModel;
            this.$emit('model-change', newModel);
        },

        // 处理树形列表滚动（用于加载更多数据）
        handleTreeScroll: _.throttle(function (event) {
            const { target } = event;
            const { scrollTop, scrollHeight, clientHeight } = target;

            // 当滚动到底部附近时加载更多数据
            if (scrollHeight - scrollTop - clientHeight < 50) {
                this.loadMoreMcpServices();
            }
        }, 100),

        // 处理树节点选中状态变化
        async handleTreeCheckChange(data, isChecked) {
            if (this.isUpdatingTreeState) return;

            const node = this.$refs.mcpTree.getNode(data);
            if (!node) return;

            this.updateCheckedCache();

            try {
                this.isUpdatingTreeState = true;

                if (data.isTool) {
                    this.handleToolNodeChange(node);
                } else {
                    await this.handleServiceNodeChange(node, data, isChecked);
                }

                this.$nextTick(() => {
                    this.updateCheckedCache();
                    this.updateSelectedServicesCount();
                });
            } finally {
                this.isUpdatingTreeState = false;
            }
        },

        // 处理服务节点状态变化
        async handleServiceNodeChange(node, data, isChecked) {
            if (isChecked) {
                await this.selectServiceWithTools(node, data);
            } else {
                this.selectAllValidToolsForService(data.id, false);
            }
        },

        // 选择服务及其工具
        async selectServiceWithTools(node, data) {
            if (!node.childNodes || node.childNodes.length === 0) {
                await this.ensureToolsLoaded(node);
                this.$nextTick(() => {
                    this.selectAllValidToolsForService(data.id, true);
                });
            } else {
                this.selectAllValidToolsForService(data.id, true);
            }
        },

        // 处理工具节点状态变化
        handleToolNodeChange(node) {
            const parentNode = node.parent;
            if (parentNode && parentNode.data && !parentNode.data.isTool) {
                this.$nextTick(() => {
                    this.updateParentServiceState(parentNode);
                });
            }
        },

        // 更新父服务节点的状态
        updateParentServiceState(serviceNode) {
            if (!serviceNode || !serviceNode.childNodes) return;

            const enabledValidTools = this.getEnabledValidTools(serviceNode);
            const serviceNodeRef = this.$refs.mcpTree.getNode(serviceNode.data.id);

            if (!serviceNodeRef) return;

            if (enabledValidTools.length === 0) {
                this.setServiceNodeState(serviceNodeRef, false, false);
                return;
            }

            const checkedEnabledTools = this.getCheckedEnabledTools(enabledValidTools);
            this.updateServiceNodeByToolsState(
                serviceNodeRef,
                checkedEnabledTools.length,
                enabledValidTools.length
            );
        },

        // 获取有效且未禁用的工具节点
        getEnabledValidTools(serviceNode) {
            return serviceNode.childNodes.filter((child) => {
                return child.data.isTool && child.data.isValid === 1 && !child.data.disabled;
            });
        },

        // 获取已选中的有效工具
        getCheckedEnabledTools(enabledValidTools) {
            return enabledValidTools.filter((child) => {
                const toolNodeRef = this.$refs.mcpTree.getNode(child.data.id);
                return toolNodeRef && toolNodeRef.checked;
            });
        },

        // 根据工具状态更新服务节点
        updateServiceNodeByToolsState(serviceNodeRef, checkedCount, totalCount) {
            if (checkedCount === 0) {
                this.setServiceNodeState(serviceNodeRef, false, false);
            } else if (checkedCount === totalCount) {
                this.setServiceNodeState(serviceNodeRef, true, false);
            } else {
                this.setServiceNodeState(serviceNodeRef, false, true);
            }
        },

        // 设置服务节点状态
        setServiceNodeState(serviceNodeRef, checked, indeterminate) {
            serviceNodeRef.setChecked(checked, false);
            serviceNodeRef.indeterminate = indeterminate;
        },

        // 更新选中的服务数量
        updateSelectedServicesCount() {
            if (!this.$refs.mcpTree) {
                this.selectedServicesCount = 0;
                return;
            }

            const checkedNodes = this.$refs.mcpTree.getCheckedNodes(false, true);
            const checkedServices = checkedNodes.filter((node) => !node.isTool);
            this.selectedServicesCount = checkedServices.length;
        },

        // 更新选中状态缓存
        updateCheckedCache() {
            if (!this.$refs.mcpTree) return;

            const checkedKeys = this.$refs.mcpTree.getCheckedKeys();
            this.checkedKeysCache = [...checkedKeys];
        },

        // 恢复选中状态
        restoreCheckedState() {
            if (!this.$refs.mcpTree || this.checkedKeysCache.length === 0) return;

            this.isUpdatingTreeState = true;

            try {
                const validToolKeys = this.getValidToolKeys();
                this.setToolNodesChecked(validToolKeys);

                this.$nextTick(() => {
                    this.updateAllServiceStates();
                });
            } finally {
                this.isUpdatingTreeState = false;
            }
        },

        // 获取有效的工具键值
        getValidToolKeys() {
            return this.checkedKeysCache.filter((key) => {
                const node = this.$refs.mcpTree.getNode(key);
                return node && node.data.isTool && node.data.isValid === 1 && !node.data.disabled;
            });
        },

        // 设置工具节点为选中状态
        setToolNodesChecked(toolKeys) {
            toolKeys.forEach((toolKey) => {
                const toolNode = this.$refs.mcpTree.getNode(toolKey);
                if (toolNode) {
                    toolNode.setChecked(true, false);
                }
            });
        },

        // 更新所有服务节点的状态
        updateAllServiceStates() {
            if (!this.$refs.mcpTree) return;

            this.mcpServices.forEach((service) => {
                const serviceNode = this.$refs.mcpTree.getNode(service.id);
                if (serviceNode && serviceNode.childNodes) {
                    this.updateParentServiceState(serviceNode);
                }
            });
        },

        // 恢复特定的选中状态
        restoreCheckedKeys(checkedKeys) {
            if (!this.$refs.mcpTree || !checkedKeys || checkedKeys.length === 0) return;

            this.isUpdatingTreeState = true;

            try {
                const existingValidToolKeys = this.getExistingValidToolKeys(checkedKeys);
                this.setToolNodesChecked(existingValidToolKeys);

                this.$nextTick(() => {
                    this.updateAllServiceStates();
                    this.updateCheckedCache();
                });
            } finally {
                this.isUpdatingTreeState = false;
            }
        },

        // 获取存在且有效的工具键值
        getExistingValidToolKeys(checkedKeys) {
            return checkedKeys.filter((key) => {
                const node = this.$refs.mcpTree.getNode(key);
                return node && node.data.isTool && node.data.isValid === 1 && !node.data.disabled;
            });
        },

        // 确保工具已加载
        async ensureToolsLoaded(node) {
            if (!node || node.data.isTool || (node.childNodes && node.childNodes.length > 0))
                return;

            try {
                let currentCheckedKeys = [];
                if (this.$refs.mcpTree) {
                    currentCheckedKeys = this.$refs.mcpTree.getCheckedKeys();
                }

                await new Promise((resolve) => {
                    this.lazyLoadNode(node, (tools) => {
                        this.updateServiceChildren(node.data.id, tools);

                        this.$nextTick(() => {
                            if (currentCheckedKeys.length > 0) {
                                this.restoreCheckedKeys(currentCheckedKeys);
                            }
                            resolve();
                        });
                    });
                });
            } catch (error) {
                console.error('加载工具失败:', error);
            }
        },

        // 更新服务的子工具
        updateServiceChildren(serviceId, tools) {
            const serviceIndex = this.mcpServices.findIndex((item) => item.id === serviceId);
            if (serviceIndex !== -1) {
                this.$set(this.mcpServices[serviceIndex], 'children', tools);
            }
        },

        // 选中/取消选中服务下的所有有效工具
        selectAllValidToolsForService(serviceId, isChecked) {
            if (!this.$refs.mcpTree || this.isUpdatingTreeState) return;

            const serviceNode = this.$refs.mcpTree.getNode(serviceId);
            if (!serviceNode || !serviceNode.childNodes) return;

            const enabledValidToolIds = this.getEnabledValidToolIds(serviceNode);

            if (enabledValidToolIds.length > 0) {
                this.setToolsCheckedState(enabledValidToolIds, isChecked);
            }
        },

        // 获取有效且未禁用的工具ID
        getEnabledValidToolIds(serviceNode) {
            return serviceNode.childNodes
                .filter(
                    (child) => child.data.isTool && child.data.isValid === 1 && !child.data.disabled
                )
                .map((child) => child.data.id);
        },

        // 设置工具的选中状态
        setToolsCheckedState(toolIds, isChecked) {
            const wasUpdating = this.isUpdatingTreeState;
            this.isUpdatingTreeState = true;

            try {
                toolIds.forEach((toolId) => {
                    const toolNode = this.$refs.mcpTree.getNode(toolId);
                    if (toolNode) {
                        toolNode.setChecked(isChecked, false);
                    }
                });

                this.$nextTick(() => {
                    if (!wasUpdating) {
                        this.updateCheckedCache();
                    }
                });
            } finally {
                this.isUpdatingTreeState = wasUpdating;
            }
        }
    },
    watch: {
        // 监听 selectedModel prop 的变化
        selectedModel: {
            handler(newVal) {
                if (newVal && newVal !== this.currentModel) {
                    this.currentModel = newVal;
                }
            },
            immediate: true
        },

        // 监听搜索文本变化，触发树形组件过滤
        mcpSearchText(val) {
            if (this.$refs.mcpTree) {
                this.$refs.mcpTree.filter(val);
            }
        }
    }
};
</script>

<style scoped lang="less">
.message-input {
    .input-container {
        display: flex;
        flex-direction: column;
        width: 60rem;
        height: 8.75rem;
        background: rgba(255, 255, 255, 0.92);
        border-radius: 0.75rem;
        border: 0.0625rem solid #1765ff;
        padding: 0.75rem;
        gap: 0.75rem;
        transition: opacity 0.3s ease;

        .chat-input {
            min-height: 0;
            flex: 1;
            resize: none;
            border: none;
            outline: none;
            font-family: HONORSansCN, HONORSansCN;
            font-weight: 400;
            color: #222222;
            font-size: 1rem;
            line-height: 1.375rem;

            &::placeholder {
                font-family: HONORSansCN, HONORSansCN;
                font-weight: 400;
                font-size: 1rem;
                color: #999999;
            }
        }

        .input-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .model-select {
                width: 9.125rem;
                height: 2rem;

                /deep/ .el-input__inner {
                    background: transparent;
                    border-radius: 0.5rem;
                    border: 0.0625rem solid #e0e0e0;
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #222222;
                }
            }
            .mcp-select {
                min-width: 9.125rem;
                height: 2rem;
                background: transparent;
                border-radius: 0.5rem;
                border: 0.0625rem solid #e0e0e0;
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                user-select: none;
                .selected-count {
                    color: #1565ff;
                    font-weight: 600;
                    margin-left: 0.25rem;
                }
                &:hover {
                    border-color: #1765ff;
                }
            }

            .right-actions {
                margin-left: auto;
                display: flex;
                align-items: center;
                gap: 1rem;

                .mic-btn {
                    width: 2rem;
                    height: 2rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: transparent;
                    border-radius: 0.375rem;
                    cursor: pointer;

                    .el-icon-microphone {
                        font-size: 1.25rem;
                        color: #0c1827;
                    }

                    &:hover {
                        background: rgba(2, 101, 254, 0.1);
                    }
                }

                .send-btn {
                    width: 2rem;
                    height: 2rem;
                    border-radius: 50%;
                    padding: 0;
                    background: #87b3fe;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background-color 0.2s;

                    .el-icon-top {
                        color: #fff;
                        font-weight: 600;
                        font-size: 1rem;
                        transform: translateY(0.0625rem);
                    }

                    .el-icon-close {
                        color: #fff;
                        font-weight: 600;
                        font-size: 1rem;
                    }

                    &.is-stop {
                        background: #f56565;

                        &:hover {
                            background: #e53e3e;
                        }
                    }

                    &.is-disabled {
                        background: rgb(200, 202, 217);
                        cursor: default;
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
.model-select-popper {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow:
        0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    .el-select-dropdown__item {
        height: fit-content;
        line-height: normal;
        padding: 0;
        &.hover {
            background: transparent;
        }
        &:hover {
            background: #f2f2f2;
        }
        &.selected {
            background: #f2f2f2;
        }
    }
    .el-select-dropdown__list {
        padding: 0;
    }
    .model-option {
        height: 3.5rem;
        border-radius: 0.5rem;
        display: grid;
        grid-template-columns: 1fr 1.25rem;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem;
        .model-option-text {
            display: flex;
            flex-direction: column;
            height: fit-content;
            flex: 1;
            .model-name {
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                line-height: 1.25rem;
            }
            .model-desc {
                font-weight: 400;
                font-size: 0.75rem;
                color: #666666;
                line-height: 1.125rem;
            }
        }
        .model-selected {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1rem;
            color: #222222;
        }
    }
}
.mic-popper {
    &.el-tooltip__popper[x-placement^='top'] .popper__arrow {
        bottom: -5px;
    }
}
.mcp-popover {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow:
        0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    // MCP Popover 样式
    .mcp-popover-content {
        padding: 0;
        height: 12rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .mcp-search-box {
            padding: 0.75rem 0;
            border-bottom: 1px solid #ebeef5;
        }

        .mcp-service-tree {
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            padding: 0;

            // 加载状态样式
            .loading-container,
            .error-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2rem 1rem;
                color: #909399;
                font-size: 0.875rem;

                i {
                    font-size: 1.5rem;
                    margin-bottom: 0.5rem;
                }

                .el-button {
                    margin-top: 0.5rem;
                }
            }

            .error-container {
                color: #f56c6c;

                i {
                    color: #f56c6c;
                }
            }

            .loading-more,
            .no-more-data {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0.5rem;
                color: #909399;
                font-size: 0.75rem;

                i {
                    margin-right: 0.25rem;
                }
            }

            .no-more-data {
                color: #c0c4cc;
            }

            // 自定义树形组件样式
            .el-tree {
                background: transparent;

                .el-tree-node {
                    .el-tree-node__content {
                        height: auto;

                        .el-tree-node__expand-icon {
                            color: #606266;
                            font-size: 0.75rem;
                            transition: transform 0.2s ease;

                            // 展开状态的图标旋转
                            &.expanded {
                                transform: rotate(90deg);
                            }

                            // 加载状态
                            &.is-loading {
                                animation: rotating 1s linear infinite;
                            }
                        }

                        // 加载动画
                        @keyframes rotating {
                            from {
                                transform: rotate(0deg);
                            }
                            to {
                                transform: rotate(360deg);
                            }
                        }

                        .el-checkbox {
                            margin-right: 0.5rem;
                            margin-bottom: 0;
                        }
                    }

                    // 父节点样式
                    &.is-expanded > .el-tree-node__content {
                        font-weight: 500;
                        color: #303133;
                    }

                    // 子节点样式
                    .el-tree-node {
                        .el-tree-node__content {
                            padding-left: 1.5rem;
                            font-size: 0.875rem;
                            color: #606266;
                        }
                    }
                }

                .custom-tree-node {
                    min-width: 0;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .node-label {
                        font-size: 0.875rem;
                        line-height: 1.25rem;
                        width: 100%;
                    }

                    .node-description {
                        font-size: 0.75rem;
                        color: #909399;
                        line-height: 1.2;
                        width: 100%;
                    }
                }
            }
        }
    }
}
</style>
