// 资源类型
const commonMimeTypes = [
    { value: 'text/plain', label: '纯文本 (.txt)' },
    { value: 'application/json', label: 'JSON 文件 (.json)' },
    { value: 'application/pdf', label: 'PDF 文档 (.pdf)' },
    { value: 'image/jpeg', label: 'JPEG 图片 (.jpg, .jpeg)' },
    { value: 'image/png', label: 'PNG 图片 (.png)' },
    { value: 'application/msword', label: 'Word 文档 (.doc)' },
    {
        value: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        label: 'Word 文档 (.docx)'
    },
    { value: 'application/vnd.ms-excel', label: 'Excel 文件 (.xls)' },
    {
        value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        label: 'Excel 文件 (.xlsx)'
    },
    { value: 'text/csv', label: 'CSV 文件 (.csv)' },
    { value: 'application/vnd.ms-powerpoint', label: 'PowerPoint (.ppt)' },
    {
        value: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        label: 'PowerPoint (.pptx)'
    },
    { value: 'application/zip', label: 'ZIP 压缩包 (.zip)' },
    { value: 'audio/mpeg', label: 'MP3 音频 (.mp3)' },
    { value: 'video/mp4', label: 'MP4 视频 (.mp4)' }
];

// 资源适用角色
const resourceAnnotationsAudienceOpts = [
    {
        label: '用户',
        value: 'user'
    },
    {
        label: '助手',
        value: 'assistant'
    }
];

export { commonMimeTypes, resourceAnnotationsAudienceOpts };