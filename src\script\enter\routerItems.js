import { loadView } from './plugin';

export default [
    {
        path: 'mcpservice/index',
        component: loadView('main/index', false),
        children: [
            {
                path: 'home',
                name: 'home',
                component: loadView('home/index', false)
            },
            {
                path: 'configTool',
                name: 'configTool',
                component: loadView('config/tool/index', false)
            },
            {
                path: 'configResource',
                name: 'configResource',
                component: loadView('config/resource/index', false)
            },
            {
                path: 'configService',
                name: 'configService',
                component: loadView('config/service/index', false)
            },
            {
                path: 'optionsContent',
                name: 'optionsContent',
                component: loadView('config/optionsContent/index', false)
            },
            {
                path: 'permission',
                name: 'permission',
                component: loadView('permission/index', false)
            },
            {
                path: 'modelTest',
                name: 'modelTest',
                component: loadView('modelTest/index', false)
            }
        ]
    },
];