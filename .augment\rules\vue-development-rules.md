---
type: 'always_apply'
---

# Vue.js 项目开发规范 - AI编程工具指南

## 项目技术栈

- Vue 2.6 + Element UI 2.13 + mtex-rams-core 平台底座
- 使用 webpack 构建，无需关注打包配置
- ESLint 配置：禁用三元表达式，函数复杂度≤15，参数≤7个，嵌套≤3层

## 核心开发规则

### 1. 新模块创建流程

#### 步骤1：创建模块目录

```
src/script/plugin2x/[模块名]/
├── indexPlugin.vue          # 必需：主入口文件
├── components/              # 可选：模块专用组件
└── [其他业务文件].vue       # 可选：其他页面
```

**命名规则**：

- 模块文件夹：kebab-case（如 `user-management`）
- 主入口文件：必须为 `indexPlugin.vue`
- 组件文件：PascalCase（如 `UserTable.vue`）

#### 步骤2：Vue组件标准结构

```vue
<template>
    <div class="[模块名]-container">
        <header class="[模块名]-container-header">页面标题</header>
        <div class="[模块名]-container-search">
            <!-- 搜索区域 -->
        </div>
        <main class="[模块名]-container-main">
            <!-- 主内容 -->
        </main>
    </div>
</template>

<script>
export default {
    name: 'ModuleName', // 大驼峰命名
    components: {},
    mixins: [],
    data() {
        return {};
    },
    created() {},
    methods: {}
};
</script>

<style scoped>
/* 使用模块名作为CSS类前缀 */
</style>
```

#### 步骤3：路由配置

在 `src/script/enter/routerItems.js` 中添加：

```javascript
{
    path: 'moduleName',                    // kebab-case
    name: 'moduleName',                    // camelCase
    component: loadView('module-name/index', false)  // 路径/index，第二参数固定false
}
```

#### 步骤4：API接口配置

在 `src/script/api/module/` 下创建 `[模块名].js`：

```javascript
export default {
    getModuleList: {
        url: '/api/module/list',
        method: 'GET'
    },
    createModule: {
        url: '/api/module/create',
        method: 'POST'
    }
};
```

### 2. 代码编写规范

#### Vue组件规范

- **组件name**：使用大驼峰命名（PascalCase）
- **CSS类名**：使用模块名前缀，如 `.user-management-container`
- **避免三元表达式**：使用 if-else 或计算属性替代
- **函数复杂度**：不超过15，参数不超过7个，嵌套不超过3层

#### 样式规范

- 使用 `scoped` 样式避免冲突
- CSS类名使用模块名作为前缀
- 遵循BEM命名规范：`.block__element--modifier`

#### 数据处理规范

- 使用 `data()` 函数返回对象
- 复杂计算使用计算属性
- 异步操作在 `created` 或 `mounted` 中处理

### 3. 常用组件和工具

#### Element UI组件

项目已集成Element UI 2.13，直接使用：

- `el-table`、`el-form`、`el-button`等
- 使用 `v-loading` 指令处理加载状态

#### 项目通用组件

位于 `src/script/components/`：

- `SearchBar`：搜索表单组件
- `DataTable`：数据表格组件
- `CardTable`：卡片表格组件
- `MetricCard`：指标卡片组件

#### 工具方法

位于 `src/script/utils/`，根据需要引入使用

### 4. 开发注意事项

#### 路由系统

- 所有路由配置在 `src/script/enter/routerItems.js`
- 使用 `loadView()` 函数加载组件
- 路径相对于 `src/script/plugin2x/`目录

#### API调用

- API配置自动合并到全局，直接使用方法名调用
- 统一的错误处理和加载状态管理

#### 样式处理

- 全局样式在 `src/style/` 目录
- 模块样式使用 `scoped` 属性
- 支持 Less、Stylus、Sass

### 5. 错误处理和调试

#### 常见问题

1. **路由无法访问**：检查 `routerItems.js` 配置和文件路径
2. **组件不显示**：检查 `indexPlugin.vue` 文件是否存在
3. **样式冲突**：确保使用 `scoped` 和模块前缀

#### 调试方法

- 使用浏览器开发者工具
- 检查控制台错误信息
- 验证网络请求状态

### 6. 性能优化建议

- 使用 `v-show` 替代频繁切换的 `v-if`
- 合理使用计算属性缓存
- 避免在模板中使用复杂表达式
- 大数据对象使用 `Object.freeze` 冻结

### 7. 代码质量要求

#### ESLint规则遵循

- 使用单引号
- 禁用三元表达式
- 函数复杂度≤15
- 函数参数≤7个
- 嵌套层次≤3层

#### 代码风格

- 变量命名具有描述性
- 添加必要注释
- 保持代码简洁清晰

---

**重要提醒**：

1. 新模块必须在 `src/script/plugin2x/` 目录下创建
2. 主入口文件必须命名为 `indexPlugin.vue`
3. 路由配置必须在 `src/script/enter/routerItems.js` 中添加
4. API接口按模块组织在 `src/script/api/module/` 目录下
5. 严格遵循命名规范和代码质量要求
