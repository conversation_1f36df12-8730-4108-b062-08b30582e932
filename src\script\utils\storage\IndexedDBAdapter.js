/**
 * IndexedDB存储适配器
 * 提供基于IndexedDB的数据存储功能，支持大量数据和复杂查询
 */
import { STORAGE_CONFIG } from '../../constant/modelTest/appConfig';

class IndexedDBAdapter {
    constructor() {
        this.dbName = STORAGE_CONFIG.indexedDB.dbName;
        this.dbVersion = STORAGE_CONFIG.indexedDB.dbVersion;
        this.db = null;

        this.stores = {
            sessions: 'sessions',
            messages: 'messages'
        };
    }

    /**
     * 初始化IndexedDB
     */
    async init() {
        return new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                reject(new Error('IndexedDB不被支持'));
                return;
            }

            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('IndexedDB打开失败'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve(true);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // 创建会话存储
                if (!db.objectStoreNames.contains(this.stores.sessions)) {
                    const sessionStore = db.createObjectStore(this.stores.sessions, {
                        keyPath: 'id'
                    });
                    sessionStore.createIndex('updatedAt', 'updatedAt', { unique: false });
                    sessionStore.createIndex('createdAt', 'createdAt', { unique: false });
                }

                // 创建消息存储
                if (!db.objectStoreNames.contains(this.stores.messages)) {
                    const messageStore = db.createObjectStore(this.stores.messages, {
                        keyPath: 'id'
                    });
                    messageStore.createIndex('sessionId', 'sessionId', { unique: false });
                    messageStore.createIndex('timestamp', 'timestamp', { unique: false });
                    messageStore.createIndex('role', 'role', { unique: false });
                }
            };
        });
    }

    /**
     * 获取事务
     */
    getTransaction(storeNames, mode = 'readonly') {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }
        return this.db.transaction(storeNames, mode);
    }

    /**
     * 获取对象存储
     */
    getStore(storeName, mode = 'readonly') {
        const transaction = this.getTransaction([storeName], mode);
        return transaction.objectStore(storeName);
    }

    // ==================== 会话操作 ====================

    /**
     * 保存会话
     */
    async saveSession(session) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.sessions, 'readwrite');
            const request = store.put(session);

            request.onsuccess = () => resolve(session);
            request.onerror = () => reject(new Error('保存会话失败'));
        });
    }

    /**
     * 获取会话
     */
    async getSession(sessionId) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.sessions);
            const request = store.get(sessionId);

            request.onsuccess = () => resolve(request.result || null);
            request.onerror = () => reject(new Error('获取会话失败'));
        });
    }

    /**
     * 删除会话
     */
    async deleteSession(sessionId) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.sessions, 'readwrite');
            const request = store.delete(sessionId);

            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(new Error('删除会话失败'));
        });
    }

    /**
     * 获取所有会话
     */
    async getAllSessions() {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.sessions);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => reject(new Error('获取会话列表失败'));
        });
    }

    // ==================== 消息操作 ====================

    /**
     * 保存消息
     */
    async saveMessage(message) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.messages, 'readwrite');

            // 先检查消息是否已存在
            const getRequest = store.get(message.id);

            getRequest.onsuccess = () => {
                // 无论是新增还是更新，都使用put方法
                const putRequest = store.put(message);

                putRequest.onsuccess = () => resolve(message);
                putRequest.onerror = () => reject(new Error('保存消息失败'));
            };

            getRequest.onerror = () => reject(new Error('检查消息失败'));
        });
    }

    /**
     * 获取消息
     */
    async getMessage(messageId) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.messages);
            const request = store.get(messageId);

            request.onsuccess = () => resolve(request.result || null);
            request.onerror = () => reject(new Error('获取消息失败'));
        });
    }

    /**
     * 删除消息
     */
    async deleteMessage(messageId) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.messages, 'readwrite');
            const request = store.delete(messageId);

            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(new Error('删除消息失败'));
        });
    }

    /**
     * 根据会话ID获取消息
     */
    async getMessagesBySession(sessionId) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.messages);
            const index = store.index('sessionId');
            const request = index.getAll(sessionId);

            request.onsuccess = () => {
                const messages = request.result || [];
                // 按时间戳排序
                messages.sort((a, b) => a.timestamp - b.timestamp);
                resolve(messages);
            };
            request.onerror = () => reject(new Error('获取会话消息失败'));
        });
    }

    /**
     * 删除会话的所有消息
     */
    async deleteMessagesBySession(sessionId) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.messages, 'readwrite');
            const index = store.index('sessionId');
            const request = index.openCursor(sessionId);

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    cursor.delete();
                    cursor.continue();
                } else {
                    resolve(true);
                }
            };

            request.onerror = () => reject(new Error('删除会话消息失败'));
        });
    }

    // ==================== 高级查询 ====================

    /**
     * 按时间范围查询会话
     */
    async getSessionsByDateRange(startDate, endDate) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.sessions);
            const index = store.index('updatedAt');
            const range = IDBKeyRange.bound(startDate, endDate);
            const request = index.getAll(range);

            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => reject(new Error('查询会话失败'));
        });
    }

    /**
     * 搜索消息内容
     */
    async searchMessages(keyword, sessionId = null) {
        return new Promise((resolve, reject) => {
            const store = this.getStore(this.stores.messages);
            const results = [];

            let request;
            if (sessionId) {
                const index = store.index('sessionId');
                request = index.openCursor(sessionId);
            } else {
                request = store.openCursor();
            }

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    const message = cursor.value;
                    if (message.content.toLowerCase().includes(keyword.toLowerCase())) {
                        results.push(message);
                    }
                    cursor.continue();
                } else {
                    resolve(results);
                }
            };

            request.onerror = () => reject(new Error('搜索消息失败'));
        });
    }

    // ==================== 通用操作 ====================

    /**
     * 清空所有数据
     */
    async clear() {
        const transaction = this.getTransaction([this.stores.sessions, this.stores.messages], 'readwrite');

        return Promise.all([
            new Promise((resolve, reject) => {
                const request = transaction.objectStore(this.stores.sessions).clear();
                request.onsuccess = () => resolve();
                request.onerror = () => reject(new Error('清空会话失败'));
            }),
            new Promise((resolve, reject) => {
                const request = transaction.objectStore(this.stores.messages).clear();
                request.onsuccess = () => resolve();
                request.onerror = () => reject(new Error('清空消息失败'));
            })
        ]);
    }

    /**
     * 获取存储使用情况
     */
    async getStorageUsage() {
        if (!navigator.storage || !navigator.storage.estimate) {
            return {
                used: 0,
                total: 0,
                percentage: 0
            };
        }

        try {
            const estimate = await navigator.storage.estimate();
            let percentage = 0;
            if (estimate.quota) {
                percentage = (estimate.usage / estimate.quota) * 100;
            }

            return {
                used: estimate.usage || 0,
                total: estimate.quota || 0,
                percentage: percentage
            };
        } catch (error) {
            console.error('获取存储使用情况失败:', error);
            return {
                used: 0,
                total: 0,
                percentage: 0
            };
        }
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}

export default IndexedDBAdapter;
