<template>
    <div class="doc-table">
        <el-form
            ref="docForm"
            :model="internalData[0]"
            class="doc-table-content"
            :class="{ 'out-border': mergedConfig.outBorder }"
        >
            <!-- keyField模式 (config.mode === 'keyField') -->
            <template v-if="mergedConfig.mode === 'keyField' && mergedConfig.keyField">
                <div
                    v-for="(item, index) in internalData"
                    :key="index"
                    class="doc-table-row"
                    :style="[
                        { gridTemplateColumns: keyGridTemplateColumns },
                        getKeyFieldRowStyle(item, mergedConfig.valueFields)
                    ]"
                >
                    <div class="doc-table-cell label-cell" :class="[`align-${mergedConfig.align}`]">
                        {{ item[mergedConfig.keyField] }}
                    </div>
                    <div
                        class="doc-table-cell value-cell"
                        :class="[`align-${mergedConfig.align}`]"
                        style="padding: 8px 12px"
                    >
                        <div
                            v-for="(col, colIndex) in mergedConfig.valueFields"
                            :key="colIndex"
                            class="value-item"
                        >
                            <div class="value-content">
                                <span class="value-label">{{ col.label }}：</span>
                                <template v-if="!col.textarea">
                                    <el-input
                                        v-model="item[col.prop]"
                                        class="edit-input keyfield-mode"
                                        :class="{ readonly: !isEdit }"
                                        clearable
                                        :readonly="!isEdit || col.editable === false"
                                        @input="handleDataChange"
                                    />
                                </template>
                                <template v-else>
                                    <textarea
                                        v-model="item[col.prop]"
                                        class="edit-textarea keyfield-mode custom-scrollbar"
                                        :class="{ readonly: !isEdit }"
                                        :readonly="!isEdit || col.editable === false"
                                        @input="handleDataChange"
                                    />
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <!-- columns模式 (使用config.columns) -->
            <template v-else-if="mergedConfig.columns && mergedConfig.columns.length">
                <template v-for="(dataItem, dataIndex) in internalData">
                    <template v-for="(groupColumns, groupIndex) in getColumnGroups()">
                        <!-- 查找此行是否有设置textareaHeight的列 -->
                        <div
                            class="doc-table-row"
                            :key="`${dataIndex}-group-${groupIndex}`"
                            :style="[
                                { gridTemplateColumns: gridTemplateColumns },
                                getRowStyle(groupColumns.find((col) => col && col.lineHeight)),
                                isFitRow(groupIndex, dataIndex) ? { flex: 1 } : {}
                            ]"
                        >
                            <template v-for="(col, colIndex) in groupColumns">
                                <template v-if="col">
                                    <div
                                        class="doc-table-cell label-cell"
                                        :class="[`align-${mergedConfig.align}`]"
                                        :key="`${dataIndex}-${groupIndex}-${colIndex}-label`"
                                    >
                                        {{ col.label }}
                                    </div>
                                    <div
                                        class="doc-table-cell value-cell"
                                        :class="[
                                            `align-${mergedConfig.align}`,
                                            col.singleLine ? 'single-line' : ''
                                        ]"
                                        :style="getValueCellStyle(groupColumns, colIndex, col)"
                                        :key="`${dataIndex}-${groupIndex}-${colIndex}-value`"
                                    >
                                        <template v-if="!col.textarea">
                                            <!-- 编辑模式 -->
                                            <template v-if="isEdit">
                                                <el-form-item
                                                    :prop="col.prop"
                                                    :rules="rules[col.prop]"
                                                    class="form-item-no-label"
                                                >
                                                    <component
                                                        :is="col.type || 'el-input'"
                                                        v-model="dataItem[col.prop]"
                                                        popper-class="mcpservice-theme"
                                                        class="edit-component normal-mode"
                                                        :class="
                                                            col.type === 'el-input' || !col.type
                                                                ? 'edit-input'
                                                                : ''
                                                        "
                                                        v-bind="col.attrs || {}"
                                                        :disabled="
                                                            col.editable === false || col.isDisabled
                                                        "
                                                        @input="handleDataChange"
                                                    >
                                                        <template v-if="col.type === 'el-select'">
                                                            <el-option
                                                                v-for="opt in col.opts || []"
                                                                :key="opt.value"
                                                                :label="opt.label"
                                                                :value="opt.value"
                                                            />
                                                        </template>
                                                    </component>
                                                </el-form-item>
                                            </template>
                                            <!-- 非编辑模式 -->
                                            <template v-else>
                                                <span class="readonly-text">
                                                    {{
                                                        col.type === 'el-select'
                                                            ? getOptionLabel(
                                                                  dataItem[col.prop],
                                                                  col.opts
                                                              )
                                                            : formatValue(dataItem[col.prop])
                                                    }}
                                                </span>
                                            </template>
                                        </template>
                                        <template v-else>
                                            <template v-if="isEdit">
                                                <el-form-item
                                                    :prop="col.prop"
                                                    :rules="rules[col.prop]"
                                                    class="form-item-no-label"
                                                >
                                                    <textarea
                                                        v-model="dataItem[col.prop]"
                                                        class="edit-textarea normal-mode custom-scrollbar"
                                                        :readonly="
                                                            !isEdit || col.editable === false
                                                        "
                                                        @input="handleDataChange"
                                                    />
                                                </el-form-item>
                                            </template>
                                            <template v-else>
                                                <textarea
                                                    v-model="dataItem[col.prop]"
                                                    class="edit-textarea normal-mode custom-scrollbar readonly"
                                                    readonly
                                                />
                                            </template>
                                        </template>
                                    </div>
                                </template>
                            </template>
                        </div>
                    </template>
                </template>
            </template>
        </el-form>
    </div>
</template>
<script>
export default {
    name: 'DocTable',
    props: {
        value: {
            type: Array,
            default: () => []
        },
        config: {
            type: Object,
            default: () => ({})
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            internalData: JSON.parse(JSON.stringify(this.value || [])),
            rules: {} // 存储所有的验证规则
        };
    },
    computed: {
        mergedConfig() {
            const defaultConfig = {
                mode: 'normal', // ['normal', 'keyField']
                columns: [],
                pairCount: 1, // 每行键值对数量（每个键值对占2列：label+value）
                labelWidth: 150, // label 单元格宽度
                keyField: '',
                valueFields: [],
                align: 'center', // ['top', 'center', 'bottom']
                outBorder: true,
                emptyText: '-'
            };

            return { ...defaultConfig, ...this.config };
        },
        gridTemplateColumns() {
            return this.generateGridTemplateColumns();
        },
        keyGridTemplateColumns() {
            return `${this.mergedConfig.labelWidth}px 1fr`;
        }
    },
    watch: {
        value: {
            handler(newVal) {
                if (JSON.stringify(newVal) !== JSON.stringify(this.internalData)) {
                    this.internalData = JSON.parse(JSON.stringify(newVal || []));
                }
            },
            deep: true
        },
        'config.columns': {
            handler(newColumns) {
                if (newColumns) {
                    this.initRules(newColumns);
                }
            },
            immediate: true
        }
    },
    methods: {
        // 初始化验证规则
        initRules(columns) {
            const rules = {};
            columns.forEach((col) => {
                if (col.rules && col.rules.length) {
                    rules[col.prop] = col.rules;
                }
            });
            this.rules = rules;
        },
        // 验证表单
        validate() {
            return new Promise((resolve, reject) => {
                if (!this.isEdit || !this.$refs.docForm) {
                    resolve(true);
                    return;
                }
                this.$refs.docForm.validate((valid) => {
                    resolve(valid);
                });
            });
        },
        // 重置验证
        resetValidation() {
            if (this.$refs.docForm) {
                this.$refs.docForm.clearValidate();
            }
        },
        handleDataChange() {
            this.$emit('input', JSON.parse(JSON.stringify(this.internalData)));
        },
        formatValue(value, emptyText = '-') {
            if (value === undefined || value === null || value === '') {
                return emptyText;
            }
            return value;
        },
        getTextareaStyle(col) {
            const style = {};
            return style;
        },

        getRowStyle(col) {
            // 设置行高
            const style = {};

            if (col && col.lineHeight) {
                if (typeof col.lineHeight === 'number') {
                    style.height = col.lineHeight + 'px';
                } else if (typeof col.lineHeight === 'string') {
                    style.height = col.lineHeight;
                }
            }

            return style;
        },
        getColumnGroups() {
            if (!this.mergedConfig.columns || !this.mergedConfig.columns.length) {
                return [];
            }

            const columns = this.mergedConfig.columns;
            const pairCount = this.mergedConfig.pairCount || 1;
            const result = [];
            let currentGroup = [];

            for (let i = 0; i < columns.length; i++) {
                const col = columns[i];

                if (col.singleLine && currentGroup.length > 0) {
                    while (currentGroup.length < pairCount) {
                        currentGroup.push(null);
                    }
                    result.push([...currentGroup]);
                    currentGroup = [];
                }

                if (col.singleLine) {
                    if (col.textarea && !col.lineHeight) {
                        col.lineHeight = 120;
                    }
                    result.push([col]);
                } else {
                    currentGroup.push(col);

                    if (currentGroup.length === pairCount || i === columns.length - 1) {
                        this.finalizeColumnGroup(
                            currentGroup,
                            pairCount,
                            i,
                            columns.length,
                            result
                        );
                        currentGroup = [];
                    }
                }
            }

            return result;
        },

        finalizeColumnGroup(currentGroup, pairCount, currentIndex, totalLength, result) {
            if (currentIndex === totalLength - 1 && currentGroup.length < pairCount) {
                while (currentGroup.length < pairCount) {
                    currentGroup.push(null);
                }
            }

            if (currentGroup.length > 0) {
                result.push([...currentGroup]);
            }
        },
        getKeyFieldRowStyle(item, valueFields) {
            const lhCol = valueFields.find((col) => col && col.lineHeight);
            if (!lhCol) return {};
            return this.getRowStyle(lhCol);
        },
        // 生成当前行的grid模板列定义，例如 pairCount=2 => '150px 1fr 150px 1fr'
        generateGridTemplateColumns() {
            const parts = [];
            for (let i = 0; i < this.mergedConfig.pairCount; i++) {
                parts.push(`${this.mergedConfig.labelWidth}px`, '1fr');
            }
            return parts.join(' ');
        },
        // 计算value单元格的gridColumn span
        getValueCellStyle(groupCols, index, col) {
            const style = {};
            const totalPairs = this.mergedConfig.pairCount;

            if (col.singleLine) {
                // 单行占满
                style.gridColumn = `span ${totalPairs * 2 - 1}`;
                return style;
            }

            // 找到后面是否还有非空列
            const hasNext = groupCols.slice(index + 1).some((c) => c);
            if (!hasNext) {
                const remainingPairs = totalPairs - (index + 1);
                const span = remainingPairs * 2 + 1;
                if (span > 1) {
                    style.gridColumn = `span ${span}`;
                }
            }
            return style;
        },
        // 计算最后需要fit的行的groupIndex
        lastFitGroupIndex() {
            const columns = this.mergedConfig.columns || [];
            if (!columns.length) return -1;
            const groups = this.getColumnGroups();
            let last = -1;
            groups.forEach((g, idx) => {
                if (g.some((c) => c && c.fit)) {
                    last = idx;
                }
            });
            return last;
        },
        isFitRow(groupIndex, dataIndex) {
            if (groupIndex !== this.lastFitGroupIndex()) return false;
            // 仅最后一条数据记录的该行用于填充剩余高度
            return dataIndex === this.internalData.length - 1;
        },
        getOptionLabel(value, options) {
            if (!options || options.length === 0) {
                return value;
            }
            const option = options.find((opt) => opt.value === value);
            return option ? option.label : value;
        }
    }
};
</script>
<style scoped lang="less">
.doc-table {
    width: 100%;

    .doc-table-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .doc-table-row {
            display: grid;
            grid-template-columns: 150px 1fr;
            border-bottom: 1px solid #ebedf0;

            &:last-child {
                border-bottom: none;
            }

            .doc-table-cell {
                padding: 8px 12px;
                border-right: 1px solid #ebedf0;
                word-break: break-all;
                font-family: PingFangSC, 'PingFang SC';
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 20px;

                &:last-child {
                    border-right: none;
                }

                &.align-top {
                    align-items: flex-start;
                }

                &.align-center {
                    align-items: center;
                }

                &.align-bottom {
                    align-items: flex-end;
                }

                &.single-line {
                    width: auto;
                }
            }

            .label-cell {
                background-color: #f6f8fa;
                display: flex;
                align-items: center;
            }

            .value-cell {
                background-color: #ffffff;
                position: relative;
                padding: 0;

                .value-item {
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .value-content {
                        display: flex;
                        align-items: flex-start;
                        width: 100%;
                    }

                    .value-label {
                        margin-right: 4px;
                        font-family: PingFangSC, 'PingFang SC';
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.85);
                        line-height: 32px;
                        flex-shrink: 0;
                    }
                }
            }
        }
    }
    .out-border {
        border: 1px solid #d6dae0;
    }

    .fit-row {
        flex: 1 1 auto;
    }
}

.edit-input {
    flex: 1;
    width: 100%;

    &.normal-mode {
        /deep/ .el-input__inner {
            width: 100%;
        }
    }

    &.keyfield-mode {
        /deep/ .el-input__inner {
            height: 32px;
        }
    }

    /deep/ .el-input__inner {
        border: none;
        outline: none;
        border-radius: 0;
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.85);
        padding: 6px 12px;
    }

    &:not(.readonly) {
        /deep/ .el-input__inner:focus {
            box-shadow:
                inset 0 0 0 1px #1565ff,
                0 0 0 2px rgba(21, 101, 255, 0.2);
        }
    }

    &.readonly {
        /deep/ .el-input__inner {
            background-color: transparent;
            box-shadow: none !important;
            cursor: default;
        }

        /deep/ .el-input__suffix {
            display: none;
        }
    }

    /deep/ .el-input__suffix {
        display: inline-flex;
        align-items: center;
    }
}

.edit-textarea {
    flex: 1;
    display: block;
    width: 100%;
    border: none;
    outline: none;
    resize: none;
    border-radius: 0;
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.85);
    padding: 6px 12px;
    box-sizing: border-box;

    &.normal-mode {
        height: 100%; /* 适应行高 */
        min-height: 60px; /* 最小高度 */
        width: 100%;
    }

    &.keyfield-mode {
        min-height: 60px;
    }

    &:not(.readonly):focus {
        box-shadow:
            inset 0 0 0 1px #1565ff,
            0 0 0 2px rgba(21, 101, 255, 0.2);
    }

    &.readonly {
        background-color: transparent;
        box-shadow: none !important;
        cursor: default;
    }
}

.edit-component {
    flex: 1;
    width: 100%;

    &.normal-mode {
        /deep/ .el-input__inner {
            width: 100%;
            border: none;
            outline: none;
            border-radius: 0;
            font-family: PingFangSC, 'PingFang SC';
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(0, 0, 0, 0.85);
            padding: 6px 12px;
        }

        /deep/ .el-input.is-focus .el-input__inner {
            box-shadow:
                inset 0 0 0 1px #1565ff,
                0 0 0 2px rgba(21, 101, 255, 0.2);
        }
    }

    &[disabled] {
        /deep/ .el-input__inner {
            background-color: transparent;
            box-shadow: none !important;
            cursor: default;
        }

        /deep/ .el-input__suffix {
            display: none;
        }
    }
}

.readonly-text {
    display: block;
    padding: 6px 12px;
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.85);
    min-height: 32px;
}

.form-item-no-label {
    height: 100%;
    margin-bottom: 0;

    /deep/ .el-form-item__content {
        height: 100%;
        line-height: normal;
    }

    /deep/ .el-form-item__error {
        position: absolute;
        display: block;
        width: fit-content;
        top: 0;
        left: 0;
        z-index: 1;
        padding: 4px 8px;
        border-radius: 4px;
        background: rgba(255, 77, 79, 0.1);
        font-size: 12px;
    }
}
</style>
