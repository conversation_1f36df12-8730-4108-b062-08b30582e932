<template>
    <div class="base-info-content">
        <div class="content-main-left">
            <DocTable
                :ref="`baseInfoDocTableRef-${value.id}`"
                style="height: 100%"
                v-model="tableData"
                :config="tableConfig"
                :isEdit="isEdit"
            />
        </div>
        <div class="content-main-right" v-if="!isEdit">
            <header class="right-header">
                服务调用地址
                <span @click="handleCopy">复制</span>
            </header>
            <div class="warning-box">
                <i class="el-icon-warning"></i>
                MCP服务的调用地址是为您分配的专属连接地址，为敏感信息，请勿对外泄漏！
            </div>
            <textarea
                v-model.trim="value.configInfo"
                class="config-info custom-scrollbar"
                readonly
            ></textarea>
        </div>
    </div>
</template>

<script>
import DocTable from '@/script/components/tables/DocTable.vue';
import { copyText } from '@/script/utils/method';

export default {
    name: 'ServiceBaseInfo',
    components: {
        DocTable
    },
    props: {
        value: {
            type: Object,
            required: true
        },
        isEdit: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tableConfig: {
                mode: 'normal',
                labelWidth: 150,
                columns: [
                    {
                        label: 'MCP名称',
                        prop: 'mcpName',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '所属分类',
                        prop: 'mcpCategory',
                        type: 'el-select',
                        attrs: {
                            placeholder: '',
                            clearable: true,
                            filterable: true
                        },
                        opts: [{ label: '默认分类', value: 1 }],
                        rules: [{ required: true, message: '必填', trigger: 'change' }]
                    },
                    {
                        label: '详细描述',
                        prop: 'mcpDescription',
                        textarea: true,
                        lineHeight: '200px',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: 'MCP服务版本号',
                        prop: 'mcpVersion',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '其他备注',
                        prop: 'comment',
                        textarea: true,
                        fit: true
                    }
                ],
                pairCount: 1,
                align: 'top',
                outBorder: false
            }
        };
    },
    computed: {
        tableData: {
            get() {
                return [
                    {
                        mcpName: this.value.mcpName,
                        mcpCategory: this.value.mcpCategory,
                        mcpDescription: this.value.mcpDescription,
                        mcpVersion: this.value.mcpVersion,
                        comment: this.value.comment
                    }
                ];
            },
            set(val) {
                this.$emit('input', {
                    ...this.value,
                    ...val[0]
                });
            }
        }
    },
    methods: {
        handleCopy() {
            copyText(this.value.configInfo)
                .then((res) => {
                    this.$message.success(res);
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        async validate() {
            return await this.$refs[`baseInfoDocTableRef-${this.value.id}`].validate();
        }
    }
};
</script>

<style lang="less" scoped>
.base-info-content {
    display: flex;
    gap: 1rem;
    .content-main-left {
        flex: 1;
        height: 100%;
        background: #ffffff;
        overflow: hidden;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
    }
    .content-main-right {
        flex: 1;
        height: 100%;
        background: #ffffff;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: 2.5rem 2rem 1fr;
        .right-header {
            background: #f6f8fa;
            box-shadow: inset 0 -0.0625rem 0 0 #ebedf0;
            border-radius: 0.1875rem 0.1875rem 0 0;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.85);
            line-height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.625rem 1rem;
            span {
                color: #1565ff;
                cursor: pointer;
                &:hover {
                    color: #1565ff99;
                }
            }
        }
        .warning-box {
            background: #fff6e6;
            border: 0.0625rem solid #ffe58f;
            padding: 0.625rem 0.75rem;
            display: flex;
            align-items: center;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.65);
            .el-icon-warning {
                color: #ff7802;
                margin-right: 8px;
            }
        }
        .config-info {
            width: 100%;
            height: 100%;
            border-radius: 0 0 0.25rem 0.25rem;
            border: none;
            outline: none;
            resize: none;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 0.875rem;
            line-height: 1.25rem;
            color: rgba(0, 0, 0, 0.85);
            padding: 0.625rem 1rem;
            &:focus:not([readonly]) {
                box-shadow:
                    inset 0 0 0 0.0625rem #1565ff,
                    0 0 0 0.125rem rgba(21, 101, 255, 0.2) !important;
            }
        }
    }
}
</style>
