<template>
    <el-popover
        class="dropdown-checkbox"
        :class="{ 'is-active': isOpen }"
        placement="bottom"
        trigger="manual"
        v-model="isOpen"
        width="auto"
        popper-class="checkbox-popover mcpservice-theme"
    >
        <div
            class="dropdown-item"
            @mouseenter="handleDropdownEnter"
            @mouseleave="handleDropdownLeave"
            @mousedown="handleDropdownMouseDown"
            @click="handleDropdownClick"
        >
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll">
                <span class="checkbox-label">全选</span>
                <span class="checkbox-value"
                    >({{ `${checkedItems.length}/${options.length}` }})
                </span>
            </el-checkbox>
            <el-checkbox-group
                class="checkbox-group"
                v-model="checkedItems"
                @change="handleCheckedItemsChange"
            >
                <el-checkbox
                    v-for="(item, index) in filteredOptions"
                    :label="getItemValue(item)"
                    :key="index"
                >
                    <span
                        class="checkbox-label"
                        :title="getItemLabel(item)"
                        v-html="highlightText(getItemLabel(item))"
                    ></span>
                    <span v-if="type !== 'value'" class="checkbox-value">{{
                        `(${item.count})`
                    }}</span>
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <div slot="reference" class="popover-trigger" @click="handleTriggerClick">
            <el-input
                ref="searchInput"
                v-model="searchText"
                :placeholder="computedPlaceholder"
                class="search-input dropdown-checkbox-input"
                @focus="handleInputFocus"
                @input="handleSearchInput"
                @click.stop="handleInputClick"
                @keydown="handleInputKeydown"
            />
            <i class="el-icon-arrow-down" :class="{ rotate: isOpen }"></i>
        </div>
    </el-popover>
</template>

<script>
export default {
    name: 'DropdownCheckBox',
    props: {
        placeholder: {
            type: String,
            default: '请输入'
        },
        /**
         * 类型
         * count: 选项名+计数
         * value: 选项名+选项值
         */
        type: {
            type: String,
            default: 'count'
        },
        /**
         * 选项列表，格式:
         * count:[{ name: '选项名', count: 0 }]
         * value:[{ label: '选项名', value: '选项值' }]
         */
        options: {
            type: Array,
            default: () => []
        },
        value: {
            type: Array,
            default: () => []
        },
        showSelectedCount: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            isIndeterminate: false,
            isOpen: false,
            checkedItems: [],
            isInternalUpdate: false, // 标记是否为内部更新，避免循环
            searchText: '', // 搜索文本
            isUserInteracting: false, // 标记用户是否正在与下拉框交互
            interactionTimer: null // 用于检测用户交互状态
        };
    },
    computed: {
        checkAll: {
            get() {
                return this.checkedItems.length === this.options.length && this.options.length > 0;
            },
            set(val) {
                if (val) {
                    this.checkedItems = this.options.map((item) => this.getItemValue(item));
                } else {
                    this.checkedItems = [];
                }
                this.isIndeterminate = false;
            }
        },
        // 过滤后的选项列表
        filteredOptions() {
            if (!this.searchText.trim()) {
                return this.options;
            }
            const searchLower = this.searchText.toLowerCase();
            return this.options.filter((item) =>
                this.getItemLabel(item).toLowerCase().includes(searchLower)
            );
        },
        computedPlaceholder() {
            return `${this.placeholder}${(this.showSelectedCount && this.checkedItems.length > 0 && '(' + this.checkedItems.length + ')') || ''}`;
        }
    },
    watch: {
        // 监听外部 value 变化，同步到内部 checkedItems
        value: {
            immediate: true,
            deep: true,
            handler(newVal) {
                if (this.isInternalUpdate) return; // 如果是内部更新触发的，跳过

                // 避免循环触发：只有当外部值与内部值不同时才更新
                if (JSON.stringify(newVal) !== JSON.stringify(this.checkedItems)) {
                    this.isInternalUpdate = true;
                    if (newVal && newVal.length > 0) {
                        this.checkedItems = [...newVal];
                    } else {
                        this.checkedItems = [];
                    }
                    this.$nextTick(() => {
                        this.isInternalUpdate = false;
                    });
                }
            }
        },
        // 当 options 改变全部选中
        options: {
            immediate: true,
            deep: true,
            handler(newOpts, oldOpts) {
                if (JSON.stringify(oldOpts) === JSON.stringify(newOpts)) {
                    return;
                }
                this.checkedItems = newOpts.map((item) => this.getItemValue(item));
            }
        },
        checkedItems: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.isInternalUpdate) return; // 如果是内部更新触发的，跳过

                // 总是向外部同步，让外部决定是否需要更新
                this.$emit('input', val);
            }
        }
    },
    methods: {
        // 根据 type 获取选项的值
        getItemValue(item) {
            if (this.type === 'value') {
                return item.value;
            }
            return item.name;
        },
        // 根据 type 获取选项的显示标签
        getItemLabel(item) {
            if (this.type === 'value') {
                return item.label;
            }
            return item.name;
        },
        handleCheckedItemsChange(value) {
            const checkedCount = value.length;
            if (checkedCount === this.options.length) {
                this.isIndeterminate = false;
            } else if (checkedCount === 0) {
                this.isIndeterminate = false;
            } else {
                this.isIndeterminate = true;
            }
        },
        // 处理触发器点击事件（点击箭头或输入框外边缘）
        handleTriggerClick(event) {
            // 如果点击的是输入框，不处理
            if (event.target.tagName === 'INPUT') {
                return;
            }
            this.toggleDropdown();
        },
        // 处理输入框点击事件
        handleInputClick() {
            this.openDropdown();
        },
        // 处理输入框获得焦点
        handleInputFocus() {
            this.openDropdown();
        },
        // 处理搜索输入
        handleSearchInput() {
            // 搜索时确保下拉框打开
            this.openDropdown();
        },
        // 处理键盘事件
        handleInputKeydown(event) {
            if (event.key === 'Escape') {
                this.closeDropdown();
                this.$refs.searchInput.blur();
            }
        },
        // 处理下拉框鼠标进入
        handleDropdownEnter() {
            this.setUserInteracting(true);
        },
        // 处理下拉框鼠标离开
        handleDropdownLeave() {
            this.setUserInteracting(false);
        },
        // 处理下拉框鼠标按下
        handleDropdownMouseDown(event) {
            // 阻止默认行为，防止输入框失焦
            event.preventDefault();
            this.setUserInteracting(true);
        },
        // 处理下拉框点击
        handleDropdownClick() {
            this.setUserInteracting(true);
            // 点击后保持输入框焦点
            this.$nextTick(() => {
                if (this.$refs.searchInput) {
                    this.$refs.searchInput.focus();
                }
            });
        },
        // 设置用户交互状态
        setUserInteracting(isInteracting) {
            this.isUserInteracting = isInteracting;

            // 清除之前的定时器
            if (this.interactionTimer) {
                clearTimeout(this.interactionTimer);
                this.interactionTimer = null;
            }

            // 如果用户停止交互，设置延迟检测
            if (!isInteracting) {
                this.interactionTimer = setTimeout(() => {
                    this.isUserInteracting = false;
                }, 100); // 短暂延迟，避免鼠标快速移动时的误判
            }
        },
        // 打开下拉框
        openDropdown() {
            if (!this.isOpen) {
                this.isOpen = true;
                this.$nextTick(() => {
                    if (this.$refs.searchInput) {
                        this.$refs.searchInput.focus();
                    }
                });
            }
        },
        // 关闭下拉框
        closeDropdown() {
            // 只有在用户没有与下拉框交互时才关闭
            if (!this.isUserInteracting) {
                this.isOpen = false;
                this.searchText = ''; // 关闭时清空搜索文本
            }
        },
        // 强制关闭下拉框（用于ESC键和外部点击）
        forceCloseDropdown() {
            this.isOpen = false;
            this.isUserInteracting = false;
            this.searchText = ''; // 关闭时清空搜索文本
            this.clearInteractionTimer();
        },
        // 切换下拉框状态
        toggleDropdown() {
            if (this.isOpen) {
                this.forceCloseDropdown();
            } else {
                this.openDropdown();
            }
        },
        // 清除交互定时器
        clearInteractionTimer() {
            if (this.interactionTimer) {
                clearTimeout(this.interactionTimer);
                this.interactionTimer = null;
            }
        },
        // 高亮匹配的文本
        highlightText(text) {
            if (!this.searchText.trim()) {
                return text;
            }
            const searchText = this.searchText.trim();
            const regex = new RegExp(`(${searchText})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        },
        // 处理文档点击事件
        handleDocumentClick(event) {
            // 如果点击的是组件外部，强制关闭下拉框
            if (!this.$el.contains(event.target)) {
                this.forceCloseDropdown();
            }
        },
        // 处理全局键盘事件
        handleDocumentKeydown(event) {
            // 如果下拉框打开且按下ESC键，关闭下拉框
            if (this.isOpen && event.key === 'Escape') {
                this.forceCloseDropdown();
                event.preventDefault();
            }
        }
    },
    mounted() {
        // 添加全局点击监听和键盘监听
        document.addEventListener('click', this.handleDocumentClick);
        document.addEventListener('keydown', this.handleDocumentKeydown);
    },
    beforeDestroy() {
        // 清理定时器和事件监听
        this.clearInteractionTimer();
        document.removeEventListener('click', this.handleDocumentClick);
        document.removeEventListener('keydown', this.handleDocumentKeydown);
    }
};
</script>

<style lang="less" scoped>
.dropdown-checkbox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 15px;
    white-space: nowrap;
}
.is-active {
    border-color: #1765ff;
}
/deep/.el-popover__reference-wrapper {
    width: 100%;
}
.popover-trigger {
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);

    .search-input {
        flex: 1;
        /deep/ .el-input__inner {
            border: none;
            padding: 0;
            height: auto;
            line-height: normal;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            background: transparent;

            &:focus {
                border: none;
                box-shadow: none;
            }
        }
        &.dropdown-checkbox-input {
            /deep/ .el-input__inner {
                &::placeholder {
                    color: rgba(0, 0, 0, 0.85);
                }
                &:focus {
                    &.el-input__inner::placeholder {
                        color: rgba(0, 0, 0, 0.35);
                    }
                }
            }
        }
    }

    .el-icon-arrow-down {
        color: #c0c4cc;
        transition: transform 0.3s;
        margin-left: 5px;
        flex-shrink: 0;

        &.rotate {
            transform: rotate(180deg);
        }
    }
}

.dropdown-item {
    width: fit-content;
    /deep/ .el-checkbox {
        margin-right: 0;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    /deep/ .el-checkbox__label {
        padding: 0;
        display: flex;
        align-items: center;
        gap: 4px;
        flex: 1;
        min-width: 40px;
    }
    .checkbox-label {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        flex: 1;
        --line-clamp: 1;
        overflow: hidden;
        display: -webkit-box;
        line-clamp: var(--line-clamp);
        -webkit-line-clamp: var(--line-clamp);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;

        /deep/ .highlight {
            background-color: #fff566;
            color: #000;
            font-weight: 500;
        }
    }
    .checkbox-value {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
}
.checkbox-group {
    display: flex;
    flex-direction: column;
    width: max-content;
    max-width: 200px;
    height: fit-content;
    max-height: 360px;
    overflow-y: auto;
    --bar-width: 4px;
    --bar-height: 4px;
    --thumb-color: #c9c9c9;
    &::-webkit-scrollbar {
        width: var(--bar-width);
        height: var(--bar-height);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background: var(--thumb-color);
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        border-radius: 8px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}
</style>
