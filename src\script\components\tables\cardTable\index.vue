<template>
    <div class="card-table">
        <div class="card-table-content custom-scrollbar" v-if="total > 0" :key="reRenderFlag">
            <SingleCard
                v-for="(item, index) in data"
                :key="index"
                :ref="`card-${index}`"
                :data="item"
                v-bind="$attrs"
                v-on="$listeners"
                @updateTable="updateTable"
                @cardActive="handleCardActive(index, $event)"
            >
                <!-- 默认插槽 -->
                <template v-for="(_, name) in $slots" v-slot:[name]="slotData">
                    <slot :name="name" v-bind="slotData"></slot>
                </template>

                <!-- 具名插槽 -->
                <template v-for="(_, name) in $scopedSlots" v-slot:[name]="slotData">
                    <slot :name="name" v-bind="slotData"></slot>
                </template>
            </SingleCard>
        </div>
        <el-empty
            v-else
            description="暂无数据"
            :image="require('@/img/common/noDataMon.png')"
            style="height: 100%"
        >
        </el-empty>
        <div class="card-table-footer" v-if="!hidePagination">
            <slot name="pagination">
                <el-pagination
                    class="pagination"
                    popper-class="mcpservice-theme"
                    :current-page="pagination.curPage"
                    :page-sizes="pagination.pageSizes"
                    :page-size="pagination.pageSize"
                    :total="total"
                    :layout="layout"
                    :pager-count="pagination.pagerCount"
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </slot>
        </div>
    </div>
</template>
<script>
import SingleCard from './SingleCard.vue';
export default {
    name: 'CardTable',
    components: {
        SingleCard
    },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        updateTable: {
            type: Function,
            default: () => {},
            validator(value) {
                return typeof value === 'function';
            }
        },
        total: {
            type: Number,
            validator: (value) => {
                return value >= 0;
            }
        },
        layout: {
            type: String,
            default: 'total, prev, pager, next, sizes, jumper'
        },
        pagination: {
            type: Object,
            default: () => ({
                curPage: 1,
                pageSize: 10
            })
        },
        hidePagination: {
            type: Boolean,
            default: false
        },
        onlyActive: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        data: {
            handler(newVal) {
                this.reRender();
            },
            deep: true
        }
    },
    data() {
        return {
            reRenderFlag: true
        };
    },
    created() {
        this.initPagination();
    },
    methods: {
        initPagination() {
            const { pageSize, pageSizes, pagerCount } = this.pagination;
            const mapPageSizes = {
                10: [10, 15, 25, 40],
                15: [15, 20, 30, 50]
            };
            if (!pageSizes || !pageSizes.length) {
                this.pagination.pageSizes = mapPageSizes[pageSize];
            }
            if (!pagerCount) {
                this.pagination.pagerCount = 7;
            }
        },
        handleSizeChange(pageSize) {
            Object.assign(this.pagination, {
                curPage: 1,
                pageSize
            });
            this.updateTable({
                curPage: 1,
                pageSize
            });
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.updateTable({
                curPage,
                pageSize: this.pagination.pageSize
            });
        },
        handleCardActive(index, { field, value }) {
            if (this.onlyActive) {
                // 将非当前点击的卡片状态设置为false，当前点击的卡片状态设置为期待值
                this.data.forEach((item, i) => {
                    this.$refs[`card-${i}`][0][field] = i === index && value;
                });
            } else {
                // 将当前点击的卡片状态设置为期待值
                this.$refs[`card-${index}`][0][field] = value;
            }
        },
        reRender() {
            this.reRenderFlag = !this.reRenderFlag;
        }
    }
};
</script>
<style scoped lang="less">
.card-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    &-content {
        width: 100%;
        min-height: 0;
        flex: 1;
        overflow-y: auto;
        // scrollbar-gutter: stable;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        --thumb-color: #8b8b8b;
    }
    &-footer {
        margin-top: 1rem;
        text-align: right;
        height: 2rem;
    }
}

/deep/.el-pagination.pagination {
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0;
    padding: 0;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.375rem;
    .el-pagination__total {
        margin-right: auto;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 0.875rem;
        color: rgba(0, 0, 0, 0.65);
        line-height: 1.375rem;
    }
    .btn-prev,
    .btn-next,
    .el-pager li {
        width: 1.875rem;
        background-color: transparent;
        border-radius: 0.125rem;
        border: 1px solid #d6dae0;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        &.active {
            background: #1565ff;
            border: none;
        }
    }
    .el-pagination__sizes,
    .el-pagination__jump {
        display: flex;
        align-items: center;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        .el-select {
            height: 100%;
        }
        .el-input {
            height: 100%;
            &__inner {
                height: 100%;
                border-radius: 0.125rem;
                border: 1px solid #d6dae0;
            }
        }
    }
}
</style>
