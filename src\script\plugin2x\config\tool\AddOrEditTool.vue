<template>
    <div class="add-or-edit-tool">
        <header class="add-or-edit-tool-header">
            <span>{{ typeInfo.title }}</span>
            <el-button style="margin-left: auto" type="plain" @click="handleCancel"
                >取消
            </el-button>
            <el-button type="primary" @click="handleConfirm">确定 </el-button>
        </header>
        <main class="add-or-edit-tool-main">
            <aside class="main-left">
                <div
                    class="main-left-item"
                    v-for="(item, index) in asideMenus"
                    :key="index"
                    :class="{ 'main-left-item-active': item.value === activeValue }"
                    @click="handleAsideClick(item.value)"
                >
                    <img :src="activeValue === item.value ? item.iconActive : item.icon" alt="" />
                    <span>{{ item.label }}</span>
                </div>
            </aside>
            <div class="main-right custom-scrollbar" ref="scrollContainer">
                <section id="toolBasicInfo" class="section-basic-info">
                    <p>工具基本信息</p>
                    <div class="section-content">
                        <SearchBar
                            ref="basicInfoFormRef"
                            :form-cols="basicInfoFormCols"
                            :form="basicInfoForm"
                        />
                    </div>
                </section>
                <section id="toolApiInfo" class="section-api-info">
                    <p>接口服务信息</p>
                    <div class="section-content">
                        <SearchBar
                            ref="apiInfoFormRef"
                            :form-cols="apiInfoFormCols"
                            :form="apiInfoForm"
                        />
                    </div>
                </section>
                <section id="toolHeaderInfo" class="section-header-info">
                    <p>工具Header参数<span class="p-btn" @click="handleAddHeaderParams">+</span></p>
                    <div class="section-content">
                        <HeaderParamsForm ref="headerParamsFormRef" v-model="paramHeader" />
                    </div>
                </section>
                <section id="toolParamsInfo" class="section-params-info">
                    <p>
                        工具参数信息
                        <el-popover placement="right" trigger="hover">
                            <div class="popover-content">
                                特殊值说明：<br />
                                1. <code>undefined</code>: 未填写，在请求时会被忽略<br />
                                2. <code>null</code>: 空值，在请求时会以null值发送<br />
                                3. <code>不填</code>:
                                不填时默认不传，相当于<code>undefined</code>，<code>string</code>类型例外<br />
                            </div>
                            <i class="el-icon-info" slot="reference"></i>
                        </el-popover>
                    </p>
                    <div class="section-content">
                        <ParamsInfoForm ref="paramsInfoFormRef" v-model="params" />
                    </div>
                </section>
                <section id="toolTest" class="section-test">
                    <p>工具调用测试</p>
                    <div class="section-content">
                        <CallTestForm
                            ref="callTestFormRef"
                            :apiInfo="apiInfoForm"
                            :paramHeader="paramHeader"
                            :params="params"
                            :paramHandle="basicInfoForm.paramHandle"
                        />
                    </div>
                </section>
            </div>
        </main>
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import HeaderParamsForm from './components/HeaderParamsForm.vue';
import ParamsInfoForm from './components/ParamsInfoForm.vue';
import CallTestForm from './components/CallTestForm.vue';
import { smoothScrollTo, scrollSpy, convertToJson } from '@/script/utils/method';
import { toolSourceOpts, paramHandleOpts, serviceMethodOpts } from '@/script/constant/tool';
import ConfigTool from '@/script/api/module/config-tool';

export default {
    name: 'AddOrEditTool',
    components: {
        SearchBar,
        HeaderParamsForm,
        ParamsInfoForm,
        CallTestForm
    },
    props: {
        type: {
            type: String,
            default: 'add'
        },
        toolId: {
            type: Number,
            default: 0
        },
        // 预填充数据
        prefillData: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            scrollSpyDisabled: false,
            activeValue: 'toolBasicInfo',
            asideMenus: [
                {
                    icon: require('@/img/common/file-close.png'),
                    iconActive: require('@/img/common/file-open.png'),
                    label: '工具基本信息',
                    value: 'toolBasicInfo'
                },
                {
                    icon: require('@/img/common/api-close.png'),
                    iconActive: require('@/img/common/api-open.png'),
                    label: '接口服务信息',
                    value: 'toolApiInfo'
                },
                {
                    icon: require('@/img/common/trend-close.png'),
                    iconActive: require('@/img/common/trend-open.png'),
                    label: '工具Header参数',
                    value: 'toolHeaderInfo'
                },
                {
                    icon: require('@/img/common/note-close.png'),
                    iconActive: require('@/img/common/note-open.png'),
                    label: '工具参数信息',
                    value: 'toolParamsInfo'
                },
                {
                    icon: require('@/img/common/order-close.png'),
                    iconActive: require('@/img/common/order-open.png'),
                    label: '工具调用测试',
                    value: 'toolTest'
                }
            ],
            // 工具基本信息
            basicInfoFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'toolName',
                        label: '工具名称：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'toolNameEn',
                        label: '工具英文名称：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'toolSource',
                        label: '工具来源：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: toolSourceOpts
                    },
                    {
                        type: 'el-select',
                        prop: 'paramHandle',
                        label: '出参处理方式：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: paramHandleOpts
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'comment',
                        label: '其他备注：',
                        labelWidth: '150px',
                        attrs: {
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'toolDescription',
                        label: '工具描述：',
                        labelWidth: '150px',
                        attrs: {
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            basicInfoForm: {
                toolName: '',
                toolNameEn: '',
                toolSource: '',
                paramHandle: '',
                comment: '',
                toolDescription: ''
            },
            // 接口服务信息
            apiInfoFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'serviceCode',
                        label: '服务编码：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'serviceName',
                        label: '服务名称：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'serviceProject',
                        label: '所属项目：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true,
                            filterable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'serviceModule',
                        label: '所属模块：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true,
                            filterable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'serviceUri',
                        label: '服务地址：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入服务地址，如：/api/service/example',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'serviceIp',
                        label: '服务IP：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入服务IP，如：***********',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'servicePort',
                        label: '服务port：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入服务port，如：8080',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'serviceMethod',
                        label: '调用方式：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: serviceMethodOpts
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'isCmccFramework',
                        label: '是否集团框架：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '是',
                                value: 1
                            },
                            {
                                label: '否',
                                value: 0
                            }
                        ]
                    },
                    {
                        type: 'el-input',
                        prop: 'serviceComment',
                        label: '其他备注：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            apiInfoForm: {
                serviceCode: '',
                serviceName: '',
                serviceProject: '',
                serviceModule: '',
                serviceUri: '',
                serviceIp: '',
                servicePort: '',
                serviceMethod: '',
                isCmccFramework: 0,
                serviceComment: ''
            },
            // 工具Header参数
            paramHeader: [],
            // 工具参数信息
            params: [],
            // 编辑接口请求需要的id
            serviceId: undefined
        };
    },
    computed: {
        typeInfo() {
            return {
                add: { title: '新增工具', funcName: 'addTool' },
                edit: { title: '编辑工具', funcName: 'updateTool' },
                copy: { title: '新增工具', funcName: 'addTool' } // 复制即带详情参数的新增
            }[this.type];
        }
    },
    watch: {
        // 监听 prefillData 的变化
        prefillData: {
            handler(newVal) {
                if (newVal) {
                    this.handlePrefillData();
                }
            },
            immediate: true,
            deep: true
        }
    },
    created() {
        // 处理预填充数据
        if (this.prefillData) {
            this.handlePrefillData();
        } else if (['edit', 'copy'].includes(this.type)) {
            ConfigTool.getToolDetail({ toolId: this.toolId }).then((res) => {
                Object.assign(this.basicInfoForm, {
                    toolName: res.data.toolName,
                    toolNameEn: res.data.toolNameEn,
                    toolSource: res.data.toolSource,
                    paramHandle: res.data.paramHandle,
                    comment: res.data.comment,
                    toolDescription: res.data.toolDescription
                });

                Object.assign(this.apiInfoForm, {
                    serviceCode: res.data.serviceCode,
                    serviceName: res.data.serviceName,
                    serviceProject: res.data.serviceProject,
                    serviceModule: res.data.serviceModule,
                    serviceUri: res.data.serviceUri,
                    serviceIp: res.data.serviceIp,
                    servicePort: res.data.servicePort,
                    serviceMethod: res.data.serviceMethod,
                    isCmccFramework: res.data.isCmccFramework,
                    serviceComment: res.data.serviceComment
                });

                this.paramHeader = res.data.paramHeader || [];
                this.params = res.data.params || [];
                this.serviceId = res.data.serviceId;
                this.$nextTick(() => {
                    this.$refs.paramsInfoFormRef.initData();
                });
            });
        }
    },
    methods: {
        handleCancel() {
            this.$emit('cancel');
        },
        async handleConfirm() {
            if (!(await this.$refs.basicInfoFormRef.validForm())) return;
            if (!(await this.$refs.apiInfoFormRef.validForm())) return;
            if (!(await this.$refs.paramsInfoFormRef.validateNowForm())) return;

            // 通用参数处理
            const commonParams = {
                ...this.basicInfoForm,
                ...this.apiInfoForm,
                paramInType: this.$refs.paramsInfoFormRef.paramsInfoRadioValue,
                paramHeader: this.paramHeader.map(
                    ({
                        paramId,
                        paramDefaultValue,
                        paramNameEn,
                        isRequired,
                        paramName,
                        paramDescription
                    }) => ({
                        paramId,
                        paramDefaultValue,
                        paramNameEn,
                        isRequired,
                        paramName,
                        paramDescription
                    })
                ),
                params: this.params.map(
                    ({
                        paramId,
                        paramName,
                        paramNameEn,
                        paramInout,
                        paramType,
                        paramDefaultValue,
                        paramDescription,
                        paramJsonPath,
                        isRequired,
                        paramComment,
                        isLeafNode,
                        paramHandleChoice
                    }) => ({
                        paramId,
                        paramName: paramName.trim(),
                        paramNameEn: paramNameEn.trim(),
                        paramInout,
                        paramType,
                        paramDefaultValue,
                        paramDescription,
                        paramJsonPath,
                        isRequired,
                        paramComment,
                        isLeafNode,
                        paramHandleChoice
                    })
                ),
                apiInJson: convertToJson(this.params.filter((item) => item.paramInout === 'IN')),
                apiOutJson: convertToJson(this.params.filter((item) => item.paramInout === 'OUT'))
            };
            // 特殊字段映射
            const specialMapping = {
                add: {
                    toolcomment: this.basicInfoForm.comment,
                    comment: this.apiInfoForm.serviceComment
                },
                edit: {
                    toolId: this.toolId,
                    serviceId: this.serviceId,
                    serviceComment: this.apiInfoForm.serviceComment
                }
            };

            const params = {
                add: {
                    ...commonParams,
                    ...specialMapping.add
                },
                edit: {
                    ...commonParams,
                    ...specialMapping.edit
                },
                copy: {
                    ...commonParams,
                    ...specialMapping.add
                }
            };

            ConfigTool[this.typeInfo.funcName](params[this.type]).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '操作成功');
                    this.$emit('confirm');
                } else {
                    this.$message.error(res.returnMsg || '操作失败');
                }
            });
        },
        handleAsideClick(value) {
            this.scrollSpyDisabled = true;
            this.activeValue = value;
            smoothScrollTo(value, 1.5, 'rem');
            setTimeout(() => {
                this.scrollSpyDisabled = false;
            }, 500);
        },
        initScrollSpy() {
            const container = this.$refs.scrollContainer;
            if (!container) return;
            this.cleanupScrollSpy = scrollSpy(
                container,
                'section',
                (id) => {
                    if (!this.scrollSpyDisabled) {
                        this.activeValue = id;
                    }
                },
                36
            );
        },
        destroyScrollSpy() {
            if (typeof this.cleanupScrollSpy === 'function') {
                this.cleanupScrollSpy();
                this.cleanupScrollSpy = null;
            }
        },
        handleAddHeaderParams() {
            this.$refs.headerParamsFormRef.addLine();
        },
        // 处理预填充数据
        handlePrefillData() {
            if (!this.prefillData) return;

            // 深拷贝辅助函数
            const deepCopy = (obj) => {
                try {
                    return JSON.parse(JSON.stringify(obj));
                } catch (e) {
                    console.error('深拷贝失败:', e);
                    return obj; // 如果失败，返回原对象（虽然这不是深拷贝）
                }
            };

            // 填充基本信息
            if (this.prefillData.basicInfoForm) {
                this.basicInfoForm = deepCopy(this.prefillData.basicInfoForm);
            }

            // 填充接口服务信息
            if (this.prefillData.apiInfoForm) {
                this.apiInfoForm = deepCopy(this.prefillData.apiInfoForm);
            }

            // 填充 Header 参数
            if (this.prefillData.paramHeader && Array.isArray(this.prefillData.paramHeader)) {
                this.paramHeader = deepCopy(this.prefillData.paramHeader);
            }

            // 填充工具参数信息
            if (this.prefillData.params && Array.isArray(this.prefillData.params)) {
                const tempParams = deepCopy(this.prefillData.params);
                // 对出参特别处理（不要默认值）
                tempParams.forEach((item) => {
                    if (item.paramInout === 'OUT') {
                        item.paramDefaultValue = '';
                    }
                });
                this.params = tempParams;
                // 确保 ParamsInfoForm 组件重新初始化数据
                this.$nextTick(() => {
                    if (this.$refs.paramsInfoFormRef) {
                        this.$refs.paramsInfoFormRef.initData();
                    }
                });
            }
        }
    },
    mounted() {
        this.$nextTick(this.initScrollSpy);
    },
    beforeDestroy() {
        this.destroyScrollSpy();
    }
};
</script>

<style scoped lang="less">
.add-or-edit-tool {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
        .main-left {
            width: 18rem;
            height: 100%;
            background: rgba(255, 255, 255, 0.96);
            border-radius: 0.375rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1.5rem 1rem;
            &-item {
                height: 2.5rem;
                border-radius: 0.375rem;
                display: flex;
                align-items: center;
                gap: 0.625rem;
                padding: 0 1rem;
                cursor: pointer;
                img {
                    width: 1.25rem;
                    height: 1.25rem;
                }
                span {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 0.875rem;
                    color: rgba(0, 0, 0, 0.85);
                }
                &-active {
                    background: #1565ff1a;
                    span {
                        font-weight: 500;
                        color: #1565ff;
                    }
                }
            }
        }
        .main-right {
            min-width: 0;
            flex: 1;
            height: 100%;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.96);
            border-radius: 0.375rem;
            padding: 1.5rem;
            section {
                &:not(:last-child) {
                    margin-bottom: 1.5rem;
                    border-bottom: 0.0625rem solid #ebedf0;
                }
                p {
                    height: 1.5rem;
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 600;
                    font-size: 1.125rem;
                    color: rgba(0, 0, 0, 0.85);
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    .p-btn {
                        font-size: 1.125rem;
                        line-height: 1.45rem;
                        color: #fff;
                        cursor: pointer;
                        display: flex;
                        justify-content: center;
                        width: 1.5rem;
                        height: 1.5rem;
                        background: #1565ff;
                        border-radius: 50%;
                        user-select: none;
                    }
                }
                .section-content {
                    padding: 1.5rem 0;
                    padding-right: 2rem;
                }
            }
        }
    }

    /deep/ .el-input__inner:focus {
        border: 0.0625rem solid #1565ff;
        box-shadow: 0 0 0 0.125rem rgba(21, 101, 255, 0.2);
    }

    /deep/ .el-textarea__inner:focus {
        border: 0.0625rem solid #1565ff;
        box-shadow: 0 0 0 0.125rem rgba(21, 101, 255, 0.2);
    }
}
</style>
<style lang="less">
.popover-content {
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
}
</style>
